# FastMode Access Manager - User Guide

This guide provides step-by-step instructions for using the FastMode Access Manager to automate user account creation in the BeiGene admin portal.

## Getting Started

### Prerequisites

Before using the application, ensure you have:

1. **Python 3.8+** installed on your Windows 11 system
2. **Internet connection** for accessing the admin portal
3. **Valid credentials** for the BeiGene admin portal
4. **Appropriate permissions** to create user accounts

### First Time Setup

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Install Playwright Browsers**
   ```bash
   playwright install
   ```

3. **Verify Configuration**
   - Ensure `config/settings.json` exists
   - Ensure `config/urls.json` exists

## Launching the Application

1. **Open Command Prompt** or PowerShell
2. **Navigate** to the FastMode directory
3. **Run the application**:
   ```bash
   python main.py
   ```

The application window will appear with the title "BeiGene Access Permission Manager".

## Using the Interface

The application interface is divided into three main sections:

### Section 1: Portal URL Selection

**Purpose**: Select and verify the target admin portal.

**Steps**:
1. **Select URL**: Choose from the dropdown list of available portals
   - "BeiGene Admin Portal" - Main production portal
   - "BeiGene Development Portal" - Development environment
   - "BeiGene Test Portal" - Test environment

2. **Open Portal** (Optional): Click "Open" to launch the selected URL in your browser
   - This allows you to manually verify the portal is accessible
   - You can log in manually if needed for verification

### Section 2: Email Input

**Purpose**: Enter the email address for user account creation.

**Steps**:
1. **Enter Email**: Type an email address in the input field
   - Must end with `@beigene.com` or `@beonemed.com`
   - Example: `<EMAIL>`

2. **Automatic Validation**: The application will automatically:
   - Validate the email format
   - Check if the domain is supported
   - Show green border for valid emails
   - Show red border for invalid emails

3. **Clear Input**: Click "Clear" to reset the input field

**Email Validation Rules**:
- ✅ Valid: `<EMAIL>`
- ✅ Valid: `<EMAIL>`
- ❌ Invalid: `<EMAIL>` (unsupported domain)
- ❌ Invalid: `invalid-email` (incorrect format)

### Section 3: Automation Control

**Purpose**: Review generated emails and start the automation process.

#### Left Side: Generated Email Addresses

This text area displays the dual-domain email addresses that will be processed:

**Example**:
If you enter `<EMAIL>`, you'll see:
```
<EMAIL>
<EMAIL>
```

#### Right Side: Control Panel

**Headless Mode Checkbox**:
- ✅ **Checked (Default)**: Browser runs invisibly in the background
- ☐ **Unchecked**: Browser window is visible (useful for debugging)

**START Button**:
- **Enabled**: When valid email addresses are generated
- **Disabled**: When no valid emails are available
- **Click**: Begins the automation process

**Status Display**:
- Shows current status: "Ready", "Automation in progress...", etc.
- Displays number of emails to be processed

## Automation Process

When you click the START button, the application performs the following sequence:

### Phase 1: Initialization
1. **Starting Playwright**: Initializes the browser automation engine
2. **Launching Browser**: Opens the browser (visible or headless based on your setting)
3. **Creating Context**: Sets up the browser environment

### Phase 2: Authentication ⚠️ **IMPORTANT**
1. **Navigate to Portal**: Goes to the admin portal URL
2. **Check Authentication**: Determines if login is required
3. **Handle Login**: If authentication is needed:
   - **Browser Window**: A browser window will appear (even in headless mode)
   - **Manual Login Required**: You must complete Microsoft OAuth login manually
   - **Status Message**: Application shows "Authentication required - please login in the browser window"
   - **Wait Period**: Application waits up to 5 minutes for you to complete login
   - **Login Steps**:
     1. Enter your BeiGene email address
     2. Enter your password
     3. Complete any 2FA if required
     4. Wait for redirect to admin portal

**Note**: The application cannot automatically handle Microsoft OAuth login for security reasons. You must manually complete the login process when prompted.

### Phase 3: Operation Group I (@beigene.com)
1. **Navigate**: Ensures we're on the admin portal page
2. **Click New**: Clicks the "New" button to open the user creation form
3. **Fill Form**: Completes all required fields:
   - Username: `<EMAIL>`
   - Real Name: `<EMAIL>`
   - AAD Switch: Enabled
   - Email: `<EMAIL>`
4. **Submit**: Clicks OK to create the user

### Phase 4: Operation Group II (@beonemed.com)
1. **Click New**: Opens another user creation form
2. **Fill Form**: Completes fields with @beonemed.com version:
   - Username: `<EMAIL>`
   - Real Name: `<EMAIL>`
   - AAD Switch: Enabled
   - Email: `<EMAIL>`
3. **Submit**: Clicks OK to create the user

### Phase 5: Completion
1. **Cleanup**: Closes browser and releases resources
2. **Results**: Shows success/failure status for each email

## Understanding Results

After automation completes, you'll see a results dialog:

### Success Example
```
Automation Results:

Successfully processed: 2 email(s)
Failed to process: 0 email(s)

Successful:
  ✓ <EMAIL>
  ✓ <EMAIL>
```

### Partial Failure Example
```
Automation Results:

Successfully processed: 1 email(s)
Failed to process: 1 email(s)

Successful:
  ✓ <EMAIL>

Failed:
  ✗ <EMAIL>
```

## Troubleshooting

### Common Issues and Solutions

#### 1. "Email validation failed"
**Problem**: Red border around email input
**Solution**:
- Check email format (must include @ and .com)
- Ensure domain is @beigene.com or @beonemed.com
- Remove extra spaces or characters

#### 2. "Authentication required - please login in the browser window"
**Problem**: Application is waiting for manual login
**Solutions**:
- Look for a browser window that opened (may be behind other windows)
- If using headless mode, uncheck "Headless Mode" and restart automation
- Complete the Microsoft login process:
  1. Enter your BeiGene email address
  2. Enter your password
  3. Complete 2FA if prompted
  4. Wait for redirect to admin portal
- If login window doesn't appear, check popup blockers
- Ensure you have valid BeiGene credentials

#### 3. "Authentication failed or timed out"
**Problem**: Login process took too long or failed
**Solutions**:
- Try again with "Headless Mode" unchecked
- Verify your credentials are correct
- Check if your account is locked or requires password reset
- Ensure stable internet connection
- Contact IT if account issues persist

#### 4. "Automation failed at navigation step"
**Problem**: Cannot access the admin portal after login
**Solutions**:
- Check internet connection
- Verify VPN connection if required
- Test portal access manually using the "Open" button
- Ensure you have proper admin permissions
- Try logging out and back in manually

#### 3. "New button is not clickable"
**Problem**: Cannot find or click the New button
**Solutions**:
- Verify you're logged into the portal
- Check if the page layout has changed
- Try using headed mode (uncheck Headless Mode) to see what's happening
- Refresh the portal page manually

#### 4. "Modal dialog did not appear"
**Problem**: Form doesn't open after clicking New
**Solutions**:
- Wait a few seconds and try again
- Check for popup blockers
- Verify portal permissions
- Use headed mode to observe the behavior

#### 5. "Input field is not ready for input"
**Problem**: Cannot fill form fields
**Solutions**:
- Ensure the form has fully loaded
- Check for JavaScript errors in the portal
- Try using headed mode to see the form state
- Verify field selectors haven't changed

### Debug Mode

To troubleshoot issues:

1. **Enable Headed Mode**: Uncheck "Headless Mode" checkbox
2. **Watch the Process**: Observe browser actions in real-time
3. **Check Logs**: Review `logs/app.log` for detailed information
4. **Manual Testing**: Use "Open" button to test portal access manually

### Log Analysis

Check the log file at `logs/app.log` for detailed information:

```
2025-08-28 15:03:22 | INFO | Starting automation for 2 emails
2025-08-28 15:03:23 | INFO | Step 1: Navigating to admin portal
2025-08-28 15:03:25 | INFO | Step 2: Clicking New button
2025-08-28 15:03:26 | ERROR | New button is not clickable
```

## Best Practices

### Before Running Automation
1. **Test Portal Access**: Use the "Open" button to verify portal accessibility
2. **Check Credentials**: Ensure you're logged into the admin portal
3. **Verify Permissions**: Confirm you have user creation permissions
4. **Start Small**: Test with one email first before processing multiple

### During Automation
1. **Don't Interfere**: Avoid using the computer during automation
2. **Monitor Progress**: Watch the status messages
3. **Be Patient**: Allow time for each step to complete
4. **Use Headed Mode**: For first-time use or troubleshooting

### After Automation
1. **Review Results**: Check the results dialog carefully
2. **Verify Creation**: Manually verify users were created in the portal
3. **Check Logs**: Review logs for any warnings or errors
4. **Document Issues**: Note any problems for future reference

## Advanced Usage

### Processing Multiple Users
Currently, the application processes one email at a time, creating both domain versions. For multiple users:

1. **Process individually**: Enter each email separately
2. **Keep records**: Document successful creations
3. **Handle failures**: Retry failed creations manually if needed

### Configuration Customization
You can modify settings in `config/settings.json`:

- **Timeouts**: Adjust wait times for slow networks
- **Browser Settings**: Change browser type or viewport
- **Logging**: Modify log levels and file settings

### URL Management
Add new portals in `config/urls.json`:

```json
{
  "name": "New Portal",
  "url": "https://new.portal.url",
  "description": "Description of the portal"
}
```

## Support and Maintenance

### Getting Help
1. **Check this guide** for common solutions
2. **Review log files** for error details
3. **Test manually** using the Open button
4. **Contact IT Help Desk** for portal-specific issues

### Regular Maintenance
1. **Update dependencies** periodically: `pip install -r requirements.txt --upgrade`
2. **Update browsers**: `playwright install`
3. **Clear logs** if they become too large
4. **Backup configuration** files before making changes

### Reporting Issues
When reporting problems, include:
1. **Error message** from the results dialog
2. **Log file contents** from `logs/app.log`
3. **Steps to reproduce** the issue
4. **Email address** that failed (if not sensitive)
5. **Browser mode** used (headed/headless)
