# API Documentation

This document provides detailed documentation for the FastMode Access Manager API and function interfaces.

## Core Modules

### Configuration Management (`src/utils/config_manager.py`)

#### ConfigManager Class

**Purpose**: Centralized configuration management for application settings.

##### Methods

**`load_settings() -> Dict[str, Any]`**
- Loads application settings from `config/settings.json`
- Returns: Dictionary containing all application settings
- Raises: `FileNotFoundError`, `json.JSONDecodeError`

**`load_urls() -> Dict[str, Any]`**
- Loads URL configurations from `config/urls.json`
- Returns: Dictionary containing URL configurations
- Raises: `FileNotFoundError`, `json.JSONDecodeError`

**`get_setting(key_path: str, default: Any = None) -> Any`**
- Retrieves specific setting using dot notation
- Parameters:
  - `key_path`: Dot-separated path (e.g., "gui.window.width")
  - `default`: Default value if setting not found
- Returns: Setting value or default

**`get_urls_list() -> List[Dict[str, str]]`**
- Returns list of configured URLs
- Returns: List of dictionaries with name, url, description keys

**`get_email_domains() -> List[str]`**
- Returns supported email domains
- Returns: List of domain strings (e.g., ["@beigene.com", "@beonemed.com"])

**`get_playwright_config() -> Dict[str, Any]`**
- Returns Playwright configuration settings
- Returns: Dictionary with browser, timeout, headless settings

### Email Utilities (`src/utils/email_utils.py`)

#### Functions

**`validate_email_address(email: str) -> bool`**
- Validates email format using email-validator library
- Parameters: `email` - Email address to validate
- Returns: True if valid format, False otherwise

**`is_supported_domain(email: str) -> bool`**
- Checks if email domain is supported
- Parameters: `email` - Email address to check
- Returns: True if domain supported, False otherwise

**`extract_username_from_email(email: str) -> Optional[str]`**
- Extracts username part from email (before @)
- Parameters: `email` - Email address
- Returns: Username string or None if invalid

**`generate_dual_domain_emails(email: str) -> List[str]`**
- Generates both beigene.com and beonemed.com versions
- Parameters: `email` - Input email address
- Returns: List containing both domain versions

**`validate_and_process_email_input(email_input: str) -> Tuple[bool, List[str], str]`**
- Complete email validation and processing
- Parameters: `email_input` - Raw email input
- Returns: Tuple of (success, email_list, error_message)

**`format_emails_for_display(emails: List[str]) -> str`**
- Formats email list for GUI display
- Parameters: `emails` - List of email addresses
- Returns: Formatted string with newlines

### Playwright Manager (`src/automation/playwright_manager.py`)

#### PlaywrightManager Class

**Purpose**: Manages Playwright browser instances and automation sessions.

##### Methods

**`start_playwright() -> bool`**
- Initializes Playwright instance
- Returns: True if successful, False otherwise

**`launch_browser(headless: Optional[bool] = None) -> bool`**
- Launches browser with configuration
- Parameters: `headless` - Override headless mode
- Returns: True if successful, False otherwise

**`create_context() -> bool`**
- Creates browser context with viewport settings
- Returns: True if successful, False otherwise

**`create_page() -> bool`**
- Creates new page in current context
- Returns: True if successful, False otherwise

**`navigate_to_url(url: str) -> bool`**
- Navigates to specified URL
- Parameters: `url` - Target URL
- Returns: True if successful, False otherwise

**`initialize_full_session(headless: Optional[bool] = None) -> bool`**
- Complete session initialization (start -> launch -> context -> page)
- Parameters: `headless` - Override headless mode
- Returns: True if all steps successful, False otherwise

**`cleanup() -> None`**
- Cleans up all Playwright resources
- Closes page, context, browser, and stops Playwright

**`get_page() -> Optional[Page]`**
- Returns current page instance
- Returns: Page object or None if not initialized

**`is_ready() -> bool`**
- Checks if session is ready for operations
- Returns: True if all components initialized, False otherwise

### Automation Operations (`src/automation/operations.py`)

#### DOM Selectors

**`DOM_SELECTORS`** - Dictionary containing CSS selectors for web elements:
- `new_button`: New user button selector
- `username_input`: Username input field selector
- `realname_input`: Real name input field selector
- `email_input`: Email input field selector
- `aad_switch`: AAD toggle switch selector
- `ok_button`: OK/Submit button selector
- `modal_dialog`: Modal dialog container selector
- `form_container`: Form container selector

#### Core Operation Functions

**`navigate_to_admin_portal(page: Page, url: str) -> bool`**
- Navigates to BeiGene admin portal
- Parameters:
  - `page`: Playwright page object
  - `url`: Admin portal URL
- Returns: True if navigation successful

**`click_new_button(page: Page) -> bool`**
- Clicks the "New" button to open user creation form
- Parameters: `page` - Playwright page object
- Returns: True if button clicked and modal opened

**`fill_user_form(page: Page, email_address: str) -> bool`**
- Fills complete user creation form
- Parameters:
  - `page`: Playwright page object
  - `email_address`: Email to use for user creation
- Returns: True if form filled and submitted successfully

#### Individual Field Functions

**`fill_username_field(page: Page, email_address: str) -> bool`**
- Fills username input field
- Validates field readiness and value setting

**`fill_realname_field(page: Page, email_address: str) -> bool`**
- Fills real name input field
- Validates field readiness and value setting

**`toggle_aad_switch(page: Page) -> bool`**
- Toggles AAD switch to enabled state
- Checks current state and only clicks if needed

**`fill_email_field(page: Page, email_address: str) -> bool`**
- Fills email input field
- Validates field readiness and value setting

**`click_ok_button(page: Page) -> bool`**
- Clicks OK button to submit form
- Waits for form submission completion

#### High-Level Workflow Functions

**`execute_operation_group_i(page: Page, beigene_email: str) -> bool`**
- Executes complete Operation Group I for @beigene.com
- Orchestrates: navigation -> new button -> form filling
- Parameters:
  - `page`: Playwright page object
  - `beigene_email`: Email with @beigene.com domain
- Returns: True if all operations successful

**`execute_operation_group_ii(page: Page, beonemed_email: str) -> bool`**
- Executes complete Operation Group II for @beonemed.com
- Similar to Group I but for different domain
- Parameters:
  - `page`: Playwright page object
  - `beonemed_email`: Email with @beonemed.com domain
- Returns: True if all operations successful

**`execute_complete_user_creation_workflow(page: Page, email_list: List[str]) -> Dict[str, bool]`**
- Executes complete workflow for multiple emails
- Processes both domains automatically
- Parameters:
  - `page`: Playwright page object
  - `email_list`: List of email addresses to process
- Returns: Dictionary mapping emails to success status

### Validation Functions (`src/automation/validators.py`)

#### Page and Element Validators

**`validate_page_loaded(page: Page, expected_url_pattern: str, timeout: int = 30000) -> bool`**
- Validates page loaded with expected URL pattern
- Waits for network idle state

**`validate_element_exists(page: Page, selector: str, timeout: int = 10000) -> bool`**
- Validates DOM element exists and is visible
- Uses configurable timeout

**`validate_element_clickable(page: Page, selector: str, timeout: int = 10000) -> bool`**
- Validates element is clickable (visible and enabled)
- Checks both visibility and enabled state

**`validate_input_field(page: Page, selector: str, timeout: int = 10000) -> bool`**
- Validates input field is ready for text input
- Checks visibility, enabled, and editable states

**`validate_modal_dialog(page: Page, modal_selector: str, timeout: int = 10000) -> bool`**
- Validates modal dialog appearance
- Waits for modal visibility

**`validate_switch_state(page: Page, switch_selector: str, expected_state: bool, timeout: int = 10000) -> bool`**
- Validates toggle switch state
- Checks aria-checked attribute against expected state

## GUI Components

### Main Window (`src/gui/main_window.py`)

#### MainWindow Class

**Purpose**: Main application window integrating all GUI components.

##### Key Methods

**`setup_ui()`**
- Initializes all GUI components and layouts
- Creates URL selector, email input, display, and control panel

**`setup_connections()`**
- Connects signals between GUI components
- Links email input to display and control panel

**`start_automation(email_list: List[str], headless_mode: bool)`**
- Initiates automation workflow
- Creates worker thread for Playwright operations

#### AutomationWorker Class

**Purpose**: Worker thread for running Playwright operations without blocking GUI.

##### Signals

- `automation_finished(dict)`: Emitted when automation completes with results
- `automation_error(str)`: Emitted when automation fails with error message
- `status_update(str)`: Emitted for status updates during execution

### GUI Components (`src/gui/components.py`)

#### URLSelectorWidget

**Purpose**: URL selection and browser opening functionality.

**Key Methods**:
- `load_urls()`: Loads URLs from configuration
- `open_selected_url()`: Opens selected URL in browser

#### EmailInputWidget

**Purpose**: Email input with real-time validation.

**Signals**:
- `emails_generated(list)`: Emitted when valid emails are generated

**Key Methods**:
- `validate_and_process_email()`: Validates and processes email input
- `clear_input()`: Clears input and resets state

#### EmailDisplayWidget

**Purpose**: Displays generated email addresses.

**Key Methods**:
- `update_emails(email_list: List[str])`: Updates displayed emails

#### ControlPanelWidget

**Purpose**: Automation control and status display.

**Signals**:
- `start_automation(list, bool)`: Emitted when START button clicked

**Key Methods**:
- `update_emails(email_list: List[str])`: Updates email list and button state
- `set_automation_running(running: bool)`: Updates UI for automation state

## Error Handling

### Exception Types

- **Configuration Errors**: `FileNotFoundError`, `json.JSONDecodeError`
- **Validation Errors**: Custom validation failures with descriptive messages
- **Playwright Errors**: Browser launch, navigation, element interaction failures
- **GUI Errors**: Thread communication, UI update failures

### Error Propagation

1. **Low-level functions**: Return boolean success/failure
2. **Mid-level functions**: Log errors and return status
3. **High-level functions**: Collect results and provide summaries
4. **GUI layer**: Display user-friendly error messages

### Logging Strategy

- **INFO**: Normal operation flow, successful actions
- **WARNING**: Recoverable issues, validation failures
- **ERROR**: Operation failures, exceptions
- **DEBUG**: Detailed execution information (when enabled)

## Extension Points

### Adding New Operations

1. **Create operation function** in `operations.py`
2. **Add DOM selectors** to `DOM_SELECTORS` dictionary
3. **Create validation functions** in `validators.py`
4. **Update workflow functions** to include new operations
5. **Test thoroughly** with both headless and headed modes

### Adding New GUI Components

1. **Create widget class** inheriting from appropriate Qt widget
2. **Implement required signals** for communication
3. **Add to main window** layout and connections
4. **Update configuration** if new settings needed

### Configuration Extensions

1. **Add settings** to appropriate JSON configuration file
2. **Update ConfigManager** methods if needed
3. **Document new settings** in configuration documentation
4. **Provide sensible defaults** for backward compatibility
