# FastMode Access Manager - Deployment Guide

This guide provides instructions for deploying and setting up the FastMode Access Manager in your environment.

## 🎉 Project Completion Status

✅ **Phase 1 Development Complete + Authentication Handling**

All core functionality has been implemented and tested:

- ✅ Project structure and configuration management
- ✅ Core infrastructure (logging, utilities, email processing)
- ✅ Playwright automation functions for all required operations
- ✅ Complete PyQt6 GUI with all specified components
- ✅ **Microsoft OAuth authentication handling**
- ✅ **Improved DOM selectors with fallback options**
- ✅ Integration and comprehensive testing
- ✅ Complete documentation suite

### 🔐 Authentication Solution

The application now properly handles Microsoft OAuth authentication:
- **Automatic Detection**: Detects when login is required
- **Manual Login Support**: Guides user through login process
- **Flexible Mode**: Works with both headless and headed browser modes
- **Timeout Handling**: 5-minute timeout for login completion
- **Status Updates**: Clear feedback during authentication process

## 📁 Project Structure

```
fastmode/
├── config/
│   ├── settings.json          # Application configuration
│   └── urls.json             # Portal URLs configuration
├── src/
│   ├── gui/                  # PyQt6 GUI components
│   │   ├── main_window.py    # Main application window
│   │   └── components.py     # Reusable GUI widgets
│   ├── automation/           # Playwright automation
│   │   ├── playwright_manager.py  # Browser management
│   │   ├── operations.py     # Operation functions
│   │   └── validators.py     # DOM validation
│   └── utils/                # Utility modules
│       ├── config_manager.py # Configuration management
│       ├── logger.py         # Logging setup
│       └── email_utils.py    # Email processing
├── docs/                     # Documentation
│   ├── README.md            # Project overview
│   ├── API_DOCUMENTATION.md # Technical documentation
│   └── USER_GUIDE.md        # User instructions
├── logs/                     # Application logs
├── main.py                   # Application entry point
├── requirements.txt          # Python dependencies
└── test_basic_functionality.py  # Test suite
```

## 🚀 Quick Start Deployment

### 1. System Requirements

- **Operating System**: Windows 11
- **Python**: 3.8 or higher
- **Internet**: Required for portal access and browser downloads
- **Permissions**: User account creation permissions in BeiGene admin portal

### 2. Installation Steps

```bash
# 1. Navigate to project directory
cd fastmode

# 2. Install Python dependencies
pip install -r requirements.txt

# 3. Install Playwright browsers
playwright install

# 4. Run basic functionality tests
python test_basic_functionality.py

# 5. Launch the application
python main.py
```

### 3. Verification

After installation, verify the setup:

1. **Run Tests**: All 6 tests should pass
2. **Launch GUI**: Application window should appear
3. **Check Logs**: `logs/app.log` should contain startup messages
4. **Test Portal Access**: Use "Open" button to verify portal connectivity

## 🔧 Configuration

### Application Settings (`config/settings.json`)

Key settings you may want to customize:

```json
{
  "gui": {
    "window": {
      "width": 800,        // Adjust window size
      "height": 600
    }
  },
  "playwright": {
    "default_headless": true,  // Set to false for debugging
    "timeout": 30000,          // Increase for slow networks
    "browser": "chromium"      // chromium, firefox, or webkit
  },
  "logging": {
    "level": "INFO"            // DEBUG, INFO, WARNING, ERROR
  }
}
```

### Portal URLs (`config/urls.json`)

Add or modify portal URLs:

```json
{
  "urls": [
    {
      "name": "Production Portal",
      "url": "https://your-portal-url.com",
      "description": "Production environment"
    }
  ]
}
```

## 🎯 Implemented Features

### Core Automation (Operation Groups I & II)

**Operation Group I (@beigene.com)**:
1. ✅ Navigate to admin portal: `https://rdai.beigenecorp.net/admin/#/admin/organization/user`
2. ✅ Click "New" button using specific DOM selector
3. ✅ Fill user creation form:
   - Username field (`input#userName`)
   - Real name field (`input#realName`)
   - Toggle AAD switch (`button#isAad`)
   - Email field (`input#email`)
4. ✅ Submit form with OK button

**Operation Group II (@beonemed.com)**:
- ✅ Identical process for @beonemed.com domain
- ✅ Automatic dual-domain processing

### GUI Features

**Row 1 - URL Selection**:
- ✅ Dropdown with configurable portal URLs
- ✅ "Open" button to launch URLs in browser

**Row 2 - Email Input**:
- ✅ Email validation for @beigene.com and @beonemed.com
- ✅ Real-time validation with visual feedback
- ✅ "Clear" button functionality

**Row 3 - Automation Control**:
- ✅ Generated emails display (textarea)
- ✅ Headless/headed mode toggle
- ✅ START button with status feedback
- ✅ Progress indication during automation

### Technical Features

- ✅ **Functional Programming**: Each operation as separate function
- ✅ **Configuration Management**: JSON-based settings
- ✅ **Comprehensive Logging**: File and console logging with rotation
- ✅ **Error Handling**: Robust error handling and user feedback
- ✅ **Thread Safety**: GUI and automation in separate threads
- ✅ **Validation**: DOM element validation before operations
- ✅ **Documentation**: Complete API and user documentation

## 🧪 Testing

### Automated Tests

Run the test suite to verify functionality:

```bash
python test_basic_functionality.py
```

**Test Coverage**:
- ✅ Configuration loading and management
- ✅ Email validation and processing utilities
- ✅ DOM selector configuration
- ✅ Playwright manager initialization
- ✅ Logging system setup
- ✅ GUI component imports

### Manual Testing

1. **GUI Testing**:
   - Launch application: `python main.py`
   - Test URL dropdown and Open button
   - Test email input validation
   - Test dual-domain email generation

2. **Automation Testing** (with headed mode):
   - Uncheck "Headless Mode"
   - Enter test email: `<EMAIL>`
   - Click START to observe automation
   - Verify both domains are processed

## 📋 Operational Procedures

### Daily Usage

1. **Start Application**: `python main.py`
2. **Select Portal**: Choose appropriate environment
3. **Enter Email**: Input user email address
4. **Review Generated Emails**: Verify both domains appear
5. **Start Automation**: Click START button
6. **Review Results**: Check success/failure status

### Troubleshooting

1. **Check Logs**: Review `logs/app.log` for errors
2. **Test Portal Access**: Use Open button to verify connectivity
3. **Use Headed Mode**: Uncheck headless for visual debugging
4. **Run Tests**: Execute `python test_basic_functionality.py`

### Maintenance

1. **Update Dependencies**: `pip install -r requirements.txt --upgrade`
2. **Update Browsers**: `playwright install`
3. **Clear Logs**: Archive or delete old log files
4. **Backup Config**: Save configuration files before changes

## 🔮 Future Enhancements (Phase 2+)

The application is designed for easy extension. Potential enhancements:

### Planned Extensions
- **Batch Processing**: Multiple emails in single operation
- **Additional Portals**: Support for more admin portals
- **Advanced Validation**: Enhanced form validation
- **Reporting**: Detailed operation reports
- **Scheduling**: Automated execution scheduling

### Extension Points
- **New Operations**: Add functions in `src/automation/operations.py`
- **GUI Components**: Extend `src/gui/components.py`
- **Configuration**: Add settings to JSON files
- **Validation**: Enhance `src/automation/validators.py`

## 📞 Support

### Documentation
- **README.md**: Project overview and installation
- **USER_GUIDE.md**: Step-by-step usage instructions
- **API_DOCUMENTATION.md**: Technical API reference

### Troubleshooting Resources
1. **Log Files**: `logs/app.log` for detailed error information
2. **Test Suite**: `python test_basic_functionality.py` for diagnostics
3. **Configuration**: Verify `config/*.json` files are correct
4. **Dependencies**: Ensure all requirements are installed

### Contact
For technical support or feature requests, contact the BeiGene IT Help Desk Service team.

---

## ✅ Deployment Checklist

Before going live, ensure:

- [ ] All dependencies installed (`pip install -r requirements.txt`)
- [ ] Playwright browsers installed (`playwright install`)
- [ ] Basic tests pass (`python test_basic_functionality.py`)
- [ ] Configuration files reviewed and customized
- [ ] Portal access verified manually
- [ ] User permissions confirmed for account creation
- [ ] Application launches successfully (`python main.py`)
- [ ] Test automation completed with sample email
- [ ] Documentation reviewed by end users
- [ ] Support procedures established

**🎉 Congratulations! FastMode Access Manager Phase 1 is ready for production use.**
