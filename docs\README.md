# FastMode Access Manager

A Python automation tool for BeiGene IT Help Desk Service to streamline access permission management for company applications.

## Overview

FastMode Access Manager is a PyQt6-based GUI application that uses <PERSON><PERSON> to automate the process of creating user accounts in the BeiGene admin portal. The tool automatically creates accounts for both `@beigene.com` and `@beonemed.com` domains from a single email input.

## Features

- **GUI Interface**: User-friendly PyQt6 interface with clear workflow steps
- **Dual Domain Support**: Automatically creates accounts for both email domains
- **Browser Automation**: Uses Playwright for reliable web automation
- **Headless/Headed Mode**: Toggle between headless and visible browser modes for debugging
- **Configuration Management**: JSON-based configuration for easy customization
- **Comprehensive Logging**: Detailed logging with file rotation
- **Error Handling**: Robust error handling and user feedback

## Project Structure

```
fastmode/
├── config/
│   ├── settings.json          # Application settings
│   └── urls.json             # Portal URLs configuration
├── src/
│   ├── gui/
│   │   ├── main_window.py    # Main application window
│   │   └── components.py     # Reusable GUI components
│   ├── automation/
│   │   ├── playwright_manager.py  # Playwright session management
│   │   ├── operations.py     # Automation operation functions
│   │   └── validators.py     # DOM validation functions
│   └── utils/
│       ├── config_manager.py # Configuration management
│       ├── logger.py         # Logging setup
│       └── email_utils.py    # Email processing utilities
├── docs/
│   ├── README.md
│   ├── API_DOCUMENTATION.md
│   └── USER_GUIDE.md
├── logs/                     # Application logs
├── requirements.txt          # Python dependencies
└── main.py                   # Application entry point
```

## Installation

### Prerequisites

- Python 3.8 or higher
- Windows 11 (as specified in requirements)
- Internet connection for downloading browser binaries

### Setup Steps

1. **Clone or download the project** to your desired directory

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Install Playwright browsers**:
   ```bash
   playwright install
   ```

4. **Verify configuration files** exist in the `config/` directory:
   - `settings.json` - Application settings
   - `urls.json` - Portal URLs

## Usage

### Starting the Application

Run the main application:
```bash
python main.py
```

### Using the Interface

The application window contains three main sections:

#### 1. Portal URL Selection
- Select the target portal from the dropdown list
- Click "Open" to launch the URL in your default browser for manual verification

#### 2. Email Input
- Enter an email address with either `@beigene.com` or `@beonemed.com` domain
- The application automatically validates the email format
- Click "Clear" to reset the input field

#### 3. Automation Control
- **Generated Emails**: Shows both domain versions of the entered email
- **Headless Mode**: Toggle browser visibility (checked = hidden, unchecked = visible)
- **START**: Begin the automation process

### Automation Process

When you click START, the application will:

1. **Operation Group I** (for @beigene.com):
   - Navigate to the admin portal
   - Click the "New" button
   - Fill the user creation form
   - Submit the form

2. **Operation Group II** (for @beonemed.com):
   - Repeat the same process for the @beonemed.com version

### Results

After completion, you'll see a results dialog showing:
- Successfully processed emails
- Failed emails (if any)
- Detailed status for each operation

## Configuration

### Application Settings (`config/settings.json`)

Key configuration options:

- **GUI Settings**: Window size, minimum dimensions
- **Playwright Settings**: Browser type, timeouts, headless mode default
- **Logging Settings**: Log level, file paths, rotation settings
- **Email Domains**: Supported email domains

### URL Configuration (`config/urls.json`)

Configure available portal URLs:
- Portal names and descriptions
- Target URLs for automation
- Additional development/test environments

## Logging

The application creates detailed logs in the `logs/` directory:

- **Console Output**: Real-time status with colored formatting
- **File Logging**: Persistent logs with rotation (10MB max, 5 backups)
- **Log Levels**: INFO, WARNING, ERROR for different event types

## Troubleshooting

### Common Issues

1. **"Playwright browsers not installed"**
   - Run: `playwright install`

2. **"Configuration file not found"**
   - Ensure `config/settings.json` and `config/urls.json` exist

3. **"Email validation failed"**
   - Verify email format and domain (@beigene.com or @beonemed.com)

4. **Automation fails**
   - Check internet connection
   - Verify portal accessibility
   - Review logs in `logs/app.log`

### Debug Mode

- Uncheck "Headless Mode" to see browser actions
- Monitor console output for real-time status
- Check log files for detailed error information

## Development

### Architecture

The application follows functional programming principles:

- **Modular Design**: Each operation is a separate function
- **Configuration-Driven**: All settings externalized to JSON files
- **Async Operations**: Playwright operations run asynchronously
- **Thread Safety**: GUI and automation run in separate threads

### Adding New Operations

To extend the automation workflow:

1. Add new operation functions in `src/automation/operations.py`
2. Update DOM selectors in the `DOM_SELECTORS` dictionary
3. Add validation functions in `src/automation/validators.py`
4. Update the main workflow function as needed

### Customization

- **GUI Layout**: Modify `src/gui/main_window.py` and `src/gui/components.py`
- **Automation Logic**: Update functions in `src/automation/operations.py`
- **Configuration**: Add new settings to `config/settings.json`

## Support

For issues or questions:

1. Check the logs in `logs/app.log`
2. Review the troubleshooting section
3. Contact the BeiGene IT Help Desk Service team

## Version History

- **v1.0.0**: Initial release with basic automation workflow
  - Dual domain email processing
  - PyQt6 GUI interface
  - Playwright automation
  - Configuration management
  - Comprehensive logging
