# FastMode Access Manager - Troubleshooting Guide

This guide addresses the common issues you encountered and provides solutions for future problems.

## 🔍 Issue Analysis: Authentication Redirect

### Problem Description
The original error you encountered:
```
Element clickability validation failed: button.ant-btn.css-iyixax.ant-btn-primary.ant-btn-color-primary.ant-btn-variant-solid
Error: Locator.wait_for: Timeout 10000ms exceeded.
- navigated to "https://login.microsoftonline.com/7dbc552d-50d7-4396-aeb9-04d0d393261b/oauth2/v2.0/authorize..."
```

### Root Cause
The BeiGene admin portal requires Microsoft OAuth authentication. When the application tried to access the portal, it was automatically redirected to the Microsoft login page, where the expected "New" button doesn't exist.

### Solution Implemented
1. **Authentication Detection**: Added `AuthenticationHandler` class to detect login requirements
2. **Manual Login Support**: Application now waits for user to complete login manually
3. **Improved Selectors**: Added fallback selectors for better element detection
4. **User Guidance**: Clear status messages guide users through the process

## 🛠️ Solutions Implemented

### 1. Authentication Handling (`src/automation/auth_handler.py`)

**Features**:
- Detects Microsoft OAuth login pages
- Waits for manual authentication completion
- Verifies admin portal access
- 5-minute timeout for login process

**Usage**:
```python
# Automatic detection and handling
if await auth_handler.is_authentication_required(page):
    await auth_handler.handle_authentication_flow(page, interactive=True)
```

### 2. Improved DOM Selectors

**Before** (Single selector):
```python
"new_button": "button.ant-btn.css-iyixax.ant-btn-primary.ant-btn-color-primary.ant-btn-variant-solid"
```

**After** (Multiple fallback selectors):
```python
"new_button": [
    "button:has-text('New')",
    "button.ant-btn:has-text('New')",
    "button[aria-label*='New']",
    "button.ant-btn.ant-btn-primary:has-text('New')",
    "button.ant-btn.css-iyixax.ant-btn-primary.ant-btn-color-primary.ant-btn-variant-solid"
]
```

### 3. Enhanced User Experience

**Status Messages**:
- "Checking authentication..."
- "Authentication required - please login in the browser window"
- "Starting user creation workflow..."

**Browser Mode Flexibility**:
- Headless mode for normal operation
- Automatic switch to headed mode for authentication
- User can manually toggle headless mode

## 📋 Step-by-Step Resolution Process

### For First-Time Users

1. **Launch Application**:
   ```bash
   python main.py
   ```

2. **Disable Headless Mode**:
   - Uncheck "Headless Mode" checkbox
   - This allows you to see the browser window

3. **Enter Email and Start**:
   - Enter test email: `<EMAIL>`
   - Click START button

4. **Complete Authentication**:
   - Browser window will open
   - Complete Microsoft login process
   - Wait for redirect to admin portal

5. **Monitor Progress**:
   - Watch status messages
   - Automation will proceed after login

### For Subsequent Uses

If you're already logged in to the BeiGene portal:
- Application will detect existing authentication
- No manual login required
- Automation proceeds directly

## 🔧 Common Issues and Solutions

### Issue 1: "Authentication required" but no browser window

**Symptoms**:
- Status shows "Authentication required"
- No browser window appears

**Solutions**:
1. Check if browser window is hidden behind other windows
2. Disable popup blockers
3. Uncheck "Headless Mode" and restart
4. Check Windows taskbar for browser icon

### Issue 2: Login window appears but automation doesn't continue

**Symptoms**:
- Successfully logged in manually
- Application still waiting for authentication

**Solutions**:
1. Ensure you're redirected to the admin portal URL
2. Wait for the full page to load
3. Check if you have admin permissions
4. Restart application if timeout occurs

### Issue 3: "New button is not clickable" after login

**Symptoms**:
- Successfully authenticated
- Cannot find or click New button

**Solutions**:
1. Verify you're on the correct admin page
2. Check if page is fully loaded
3. Look for permission issues
4. Try refreshing the page manually

### Issue 4: Form fields not filling correctly

**Symptoms**:
- New button clicked successfully
- Form fields not being filled

**Solutions**:
1. Check if modal dialog opened
2. Wait for form to fully load
3. Verify field selectors are correct
4. Check for JavaScript errors in browser

## 🧪 Testing and Validation

### Run Authentication Tests
```bash
python test_auth_handling.py
```

**Expected Output**:
- ✓ Auth Handler Import: PASS
- ✓ Selector Improvements: PASS  
- ✓ Auth Detection (Live): PASS

### Run Basic Functionality Tests
```bash
python test_basic_functionality.py
```

**Expected Output**:
- All 6 tests should pass
- No import errors
- Configuration loaded correctly

### Manual Testing Steps

1. **Test Portal Access**:
   - Use "Open" button in GUI
   - Verify manual access to admin portal
   - Confirm you can see user management page

2. **Test Email Processing**:
   - Enter various email formats
   - Verify dual-domain generation
   - Check validation feedback

3. **Test Automation Flow**:
   - Start with headed mode
   - Complete authentication manually
   - Monitor each automation step

## 📞 Getting Help

### Log Analysis

Check `logs/app.log` for detailed information:
```bash
# View recent logs
tail -n 50 logs/app.log

# Search for errors
findstr "ERROR" logs/app.log
```

### Debug Mode

For troubleshooting:
1. Uncheck "Headless Mode"
2. Watch browser actions in real-time
3. Check browser developer tools (F12)
4. Monitor network requests

### Common Log Messages

**Normal Operation**:
```
INFO | Authentication required - please login in the browser window
INFO | Successfully navigated to admin portal
INFO | New button clicked using selector: button:has-text('New')
```

**Error Indicators**:
```
ERROR | Authentication failed or timed out
ERROR | New button is not clickable with any selector
ERROR | Modal dialog did not appear after clicking New button
```

## 🔮 Future Improvements

### Planned Enhancements
1. **Session Persistence**: Remember authentication state
2. **Batch Processing**: Handle multiple emails efficiently
3. **Error Recovery**: Automatic retry mechanisms
4. **Advanced Logging**: More detailed operation tracking

### Configuration Options

You can customize behavior in `config/settings.json`:

```json
{
  "playwright": {
    "timeout": 30000,        // Increase for slow networks
    "default_headless": false // Default to headed mode
  },
  "authentication": {
    "timeout": 300000,       // 5 minutes for login
    "retry_attempts": 3      // Number of retry attempts
  }
}
```

## ✅ Success Indicators

### Application Working Correctly
- All tests pass
- GUI launches without errors
- Portal URLs load correctly
- Email validation works
- Authentication detection functions

### Automation Working Correctly
- Browser opens (in headed mode)
- Authentication completes successfully
- Admin portal loads
- New button found and clicked
- Form fields filled correctly
- Users created successfully

### Troubleshooting Complete
- No authentication timeouts
- All DOM elements found
- Form submission successful
- Results dialog shows success
- Log files show no errors

---

**Remember**: The application is designed to be robust and user-friendly. Most issues can be resolved by:
1. Using headed mode for visibility
2. Ensuring proper authentication
3. Checking log files for details
4. Following the step-by-step guides
