"""
Email Utilities Module

This module provides utility functions for email address processing and validation.
It handles email domain conversion and validation for the access management system.

Author: BeiGene IT Help Desk Service
Date: 2025-08-28
"""

import re
from typing import List, Tuple, Optional
from email_validator import validate_email, EmailNotValidError
from loguru import logger

from .config_manager import config_manager


def validate_email_address(email: str) -> bool:
    """
    Validate if the provided email address is in correct format.
    
    Args:
        email (str): Email address to validate
        
    Returns:
        bool: True if email is valid, False otherwise
    """
    try:
        validate_email(email)
        return True
    except EmailNotValidError:
        logger.warning(f"Invalid email format: {email}")
        return False


def is_supported_domain(email: str) -> bool:
    """
    Check if the email domain is supported by the application.
    
    Args:
        email (str): Email address to check
        
    Returns:
        bool: True if domain is supported, False otherwise
    """
    supported_domains = config_manager.get_email_domains()
    
    for domain in supported_domains:
        if email.lower().endswith(domain.lower()):
            return True
    
    logger.warning(f"Unsupported email domain: {email}")
    return False


def extract_username_from_email(email: str) -> Optional[str]:
    """
    Extract username part from email address (part before @).
    
    Args:
        email (str): Email address
        
    Returns:
        Optional[str]: Username part or None if invalid email
    """
    if not validate_email_address(email):
        return None
        
    try:
        username = email.split('@')[0]
        return username.strip()
    except IndexError:
        logger.error(f"Failed to extract username from: {email}")
        return None


def generate_dual_domain_emails(email: str) -> List[str]:
    """
    Generate both beigene.com and beonemed.com versions of an email address.
    
    This function takes an email address and creates two versions:
    one with @beigene.com domain and one with @beonemed.com domain.
    
    Args:
        email (str): Input email address
        
    Returns:
        List[str]: List containing both domain versions of the email
        
    Example:
        >>> generate_dual_domain_emails("<EMAIL>")
        ["<EMAIL>", "<EMAIL>"]
    """
    username = extract_username_from_email(email)
    if not username:
        logger.error(f"Cannot generate dual domain emails for invalid email: {email}")
        return []
    
    dual_emails = [
        f"{username}@beigene.com",
        f"{username}@beonemed.com"
    ]
    
    logger.info(f"Generated dual domain emails for {username}: {dual_emails}")
    return dual_emails


def validate_and_process_email_input(email_input: str) -> Tuple[bool, List[str], str]:
    """
    Validate and process email input, generating dual domain versions.
    
    Args:
        email_input (str): Raw email input from user
        
    Returns:
        Tuple[bool, List[str], str]: 
            - Success status
            - List of generated email addresses
            - Error message (empty if successful)
    """
    email_input = email_input.strip()
    
    # Check if input is empty
    if not email_input:
        return False, [], "Email address cannot be empty"
    
    # Validate email format
    if not validate_email_address(email_input):
        return False, [], "Invalid email address format"
    
    # Check if domain is supported
    if not is_supported_domain(email_input):
        supported_domains = config_manager.get_email_domains()
        return False, [], f"Email domain must be one of: {', '.join(supported_domains)}"
    
    # Generate dual domain emails
    dual_emails = generate_dual_domain_emails(email_input)
    
    if not dual_emails:
        return False, [], "Failed to generate email addresses"
    
    return True, dual_emails, ""


def format_emails_for_display(emails: List[str]) -> str:
    """
    Format email list for display in textarea.
    
    Args:
        emails (List[str]): List of email addresses
        
    Returns:
        str: Formatted string with each email on a new line
    """
    return '\n'.join(emails)
