"""
Authentication Handler Module

This module handles Microsoft OAuth authentication for the BeiGene admin portal.
It provides functions to detect authentication requirements and handle login flows.

Author: BeiGene IT Help Desk Service
Date: 2025-08-28
"""

import asyncio
from typing import Optional, Dict, Any
from playwright.async_api import Page
from loguru import logger

from .validators import validate_page_loaded, validate_element_exists


class AuthenticationHandler:
    """
    Handles authentication flows for the BeiGene admin portal.
    
    This class provides methods to detect when authentication is required
    and handle Microsoft OAuth login flows.
    """
    
    def __init__(self):
        """Initialize the authentication handler."""
        self.microsoft_login_patterns = [
            "login.microsoftonline.com",
            "oauth2/v2.0/authorize",
            "microsoft.com/oauth"
        ]
        
        self.admin_portal_patterns = [
            "rdai.beigenecorp.net/admin",
            "admin/#/admin/organization/user"
        ]
    
    async def is_authentication_required(self, page: Page) -> bool:
        """
        Check if the current page requires authentication.
        
        Args:
            page (Page): Playwright page object
            
        Returns:
            bool: True if authentication is required, False otherwise
        """
        try:
            current_url = page.url
            logger.info(f"Checking authentication for URL: {current_url}")
            
            # Check if we're on a Microsoft login page
            for pattern in self.microsoft_login_patterns:
                if pattern in current_url.lower():
                    logger.info(f"Authentication required - detected Microsoft login: {pattern}")
                    return True
            
            # Check if we're already on the admin portal
            for pattern in self.admin_portal_patterns:
                if pattern in current_url.lower():
                    logger.info("Already authenticated - on admin portal")
                    return False
            
            # Check for common login indicators
            login_indicators = [
                "input[type='email']",
                "input[name='loginfmt']",
                "input[placeholder*='email']",
                "#i0116",  # Microsoft email input ID
                ".login-form",
                "[data-testid='i0116']"
            ]
            
            for selector in login_indicators:
                try:
                    if await page.locator(selector).count() > 0:
                        logger.info(f"Authentication required - found login element: {selector}")
                        return True
                except:
                    continue
            
            logger.info("No authentication required")
            return False
            
        except Exception as e:
            logger.error(f"Error checking authentication status: {str(e)}")
            return True  # Assume auth required if we can't determine
    
    async def wait_for_manual_authentication(self, page: Page, timeout: int = 300000) -> bool:
        """
        Wait for user to manually complete authentication.
        
        This function monitors the page and waits for the user to manually
        log in through the browser. It will return when authentication is complete
        or timeout is reached.
        
        Args:
            page (Page): Playwright page object
            timeout (int): Timeout in milliseconds (default: 5 minutes)
            
        Returns:
            bool: True if authentication completed successfully, False if timeout
        """
        logger.info("Waiting for manual authentication...")
        logger.info("Please complete the login process in the browser window")
        
        start_time = asyncio.get_event_loop().time()
        check_interval = 2000  # Check every 2 seconds
        
        while True:
            try:
                # Check if we've reached the admin portal
                current_url = page.url
                
                for pattern in self.admin_portal_patterns:
                    if pattern in current_url.lower():
                        logger.info(f"Authentication completed - reached admin portal: {current_url}")
                        return True
                
                # Check if authentication is no longer required
                if not await self.is_authentication_required(page):
                    logger.info("Authentication completed - no longer on login page")
                    return True
                
                # Check timeout
                elapsed_time = (asyncio.get_event_loop().time() - start_time) * 1000
                if elapsed_time > timeout:
                    logger.error(f"Authentication timeout after {timeout/1000} seconds")
                    return False
                
                # Wait before next check
                await page.wait_for_timeout(check_interval)
                
            except Exception as e:
                logger.error(f"Error during authentication wait: {str(e)}")
                return False
    
    async def handle_authentication_flow(self, page: Page, interactive: bool = True) -> bool:
        """
        Handle the complete authentication flow.
        
        Args:
            page (Page): Playwright page object
            interactive (bool): Whether to wait for manual authentication
            
        Returns:
            bool: True if authentication successful, False otherwise
        """
        try:
            # Check if authentication is required
            if not await self.is_authentication_required(page):
                logger.info("No authentication required")
                return True
            
            if interactive:
                logger.info("Interactive authentication mode - waiting for manual login")
                return await self.wait_for_manual_authentication(page)
            else:
                logger.error("Non-interactive mode not supported for Microsoft OAuth")
                return False
                
        except Exception as e:
            logger.error(f"Authentication flow failed: {str(e)}")
            return False
    
    async def verify_admin_portal_access(self, page: Page) -> bool:
        """
        Verify that we have successfully accessed the admin portal.
        
        Args:
            page (Page): Playwright page object
            
        Returns:
            bool: True if admin portal is accessible, False otherwise
        """
        try:
            current_url = page.url
            logger.info(f"Verifying admin portal access: {current_url}")
            
            # Check URL patterns
            for pattern in self.admin_portal_patterns:
                if pattern in current_url.lower():
                    logger.info(f"Admin portal access verified: {pattern}")
                    
                    # Additional verification - look for admin-specific elements
                    admin_indicators = [
                        "button:has-text('New')",
                        ".ant-btn:has-text('New')",
                        "[data-testid='new-user-button']",
                        ".user-management",
                        ".admin-panel"
                    ]
                    
                    for selector in admin_indicators:
                        try:
                            if await page.locator(selector).count() > 0:
                                logger.info(f"Admin functionality confirmed: {selector}")
                                return True
                        except:
                            continue
                    
                    # Even if we don't find specific elements, URL match is good enough
                    logger.info("Admin portal access verified by URL")
                    return True
            
            logger.error("Admin portal access not verified - URL doesn't match expected patterns")
            return False
            
        except Exception as e:
            logger.error(f"Error verifying admin portal access: {str(e)}")
            return False


# Global authentication handler instance
auth_handler = AuthenticationHandler()
