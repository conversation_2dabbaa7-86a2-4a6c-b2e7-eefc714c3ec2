"""
Main Window Module

This module contains the main application window for the FastMode Access Manager.
It integrates all GUI components and handles the automation workflow.

Author: BeiGene IT Help Desk Service
Date: 2025-08-28
"""

import asyncio
import sys
from typing import List, Dict, Any
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QMessageBox, QApplication, QFrame
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon
from loguru import logger

from .components import URLSelectorWidget, EmailInputWidget, EmailDisplayWidget, ControlPanelWidget
from ..automation.playwright_manager import PlaywrightManager
from ..automation.operations import execute_complete_user_creation_workflow
from ..utils.config_manager import config_manager


class AutomationWorker(QThread):
    """
    Worker thread for running Playwright automation operations.
    
    This thread handles the asynchronous Playwright operations to prevent
    blocking the GUI thread during automation execution.
    """
    
    # Signals for communication with main thread
    automation_finished = pyqtSignal(dict)  # Results dictionary
    automation_error = pyqtSignal(str)      # Error message
    status_update = pyqtSignal(str)         # Status message
    
    def __init__(self, email_list: List[str], headless_mode: bool):
        super().__init__()
        self.email_list = email_list
        self.headless_mode = headless_mode
        self.playwright_manager = PlaywrightManager()
        
    def run(self):
        """Run the automation workflow in a separate thread."""
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Run the automation
            results = loop.run_until_complete(self.run_automation())
            
            # Emit results
            self.automation_finished.emit(results)
            
        except Exception as e:
            logger.error(f"Automation worker error: {str(e)}")
            self.automation_error.emit(str(e))
        finally:
            # Clean up event loop
            try:
                loop.close()
            except:
                pass
                
    async def run_automation(self) -> Dict[str, bool]:
        """Run the complete automation workflow."""
        try:
            self.status_update.emit("Initializing Playwright...")

            # Initialize Playwright session
            if not await self.playwright_manager.initialize_full_session(self.headless_mode):
                raise Exception("Failed to initialize Playwright session")

            self.status_update.emit("Checking authentication...")

            # Get the page instance
            page = self.playwright_manager.get_page()
            if not page:
                raise Exception("Failed to get page instance")

            # Navigate to portal first to check authentication
            admin_url = "https://rdai.beigenecorp.net/admin/#/admin/organization/user"
            await page.goto(admin_url, wait_until="networkidle", timeout=30000)

            # Import auth handler here to avoid circular imports
            from ..automation.auth_handler import auth_handler

            # Check if authentication is required
            if await auth_handler.is_authentication_required(page):
                self.status_update.emit("Authentication required - please login in the browser window")

                # Handle authentication
                if not await auth_handler.handle_authentication_flow(page, interactive=True):
                    raise Exception("Authentication failed or timed out. Please ensure you have valid credentials and try again.")

            self.status_update.emit("Starting user creation workflow...")

            # Execute the complete workflow
            results = await execute_complete_user_creation_workflow(page, self.email_list)

            self.status_update.emit("Automation completed")
            return results
            
        except Exception as e:
            logger.error(f"Automation execution error: {str(e)}")
            raise
        finally:
            # Clean up Playwright resources
            await self.playwright_manager.cleanup()


class MainWindow(QMainWindow):
    """
    Main application window for FastMode Access Manager.
    
    This window integrates all GUI components and manages the automation workflow.
    """
    
    def __init__(self):
        super().__init__()
        self.automation_worker = None
        self.setup_ui()
        self.setup_connections()
        self.load_window_settings()
        
    def setup_ui(self):
        """Set up the main window UI components."""
        # Set window properties
        app_config = config_manager.get_setting("application", {})
        window_title = app_config.get("window_title", "FastMode Access Manager")
        self.setWindowTitle(window_title)
        
        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # Title label
        title_label = QLabel(window_title)
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)
        
        # Add separator
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.Shape.HLine)
        separator1.setFrameShadow(QFrame.Shadow.Sunken)
        main_layout.addWidget(separator1)
        
        # Row 1: URL Selector
        url_label = QLabel("1. Select Portal URL:")
        url_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        main_layout.addWidget(url_label)
        
        self.url_selector = URLSelectorWidget()
        main_layout.addWidget(self.url_selector)
        
        # Add separator
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.Shape.HLine)
        separator2.setFrameShadow(QFrame.Shadow.Sunken)
        main_layout.addWidget(separator2)
        
        # Row 2: Email Input
        email_label = QLabel("2. Enter Email Address:")
        email_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        main_layout.addWidget(email_label)
        
        self.email_input = EmailInputWidget()
        main_layout.addWidget(self.email_input)
        
        # Add separator
        separator3 = QFrame()
        separator3.setFrameShape(QFrame.Shape.HLine)
        separator3.setFrameShadow(QFrame.Shadow.Sunken)
        main_layout.addWidget(separator3)
        
        # Row 3: Email Display and Control Panel
        workflow_label = QLabel("3. Generated Emails and Automation Control:")
        workflow_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        main_layout.addWidget(workflow_label)
        
        # Horizontal layout for email display and control panel
        row3_layout = QHBoxLayout()
        
        # Email display (left side)
        self.email_display = EmailDisplayWidget()
        row3_layout.addWidget(self.email_display, 2)  # 2/3 of the width
        
        # Control panel (right side)
        self.control_panel = ControlPanelWidget()
        row3_layout.addWidget(self.control_panel, 1)  # 1/3 of the width
        
        main_layout.addLayout(row3_layout)
        
        # Add stretch to push everything to the top
        main_layout.addStretch()
        
    def setup_connections(self):
        """Set up signal-slot connections between components."""
        # Connect email input to email display and control panel
        self.email_input.emails_generated.connect(self.email_display.update_emails)
        self.email_input.emails_generated.connect(self.control_panel.update_emails)
        
        # Connect control panel start signal
        self.control_panel.start_automation.connect(self.start_automation)
        
    def load_window_settings(self):
        """Load and apply window settings from configuration."""
        window_config = config_manager.get_setting("gui.window", {})
        
        # Set window size
        width = window_config.get("width", 800)
        height = window_config.get("height", 600)
        self.resize(width, height)
        
        # Set minimum size
        min_width = window_config.get("min_width", 600)
        min_height = window_config.get("min_height", 400)
        self.setMinimumSize(min_width, min_height)
        
        # Center the window
        self.center_window()
        
    def center_window(self):
        """Center the window on the screen."""
        screen = QApplication.primaryScreen()
        if screen:
            screen_geometry = screen.availableGeometry()
            window_geometry = self.frameGeometry()
            center_point = screen_geometry.center()
            window_geometry.moveCenter(center_point)
            self.move(window_geometry.topLeft())
            
    def start_automation(self, email_list: List[str], headless_mode: bool):
        """Start the automation workflow."""
        logger.info(f"Starting automation for {len(email_list)} emails, headless: {headless_mode}")
        
        # Validate email list
        if not email_list:
            QMessageBox.warning(self, "Warning", "No email addresses to process")
            return
            
        # Update UI state
        self.control_panel.set_automation_running(True)
        
        # Create and start automation worker
        self.automation_worker = AutomationWorker(email_list, headless_mode)
        self.automation_worker.automation_finished.connect(self.on_automation_finished)
        self.automation_worker.automation_error.connect(self.on_automation_error)
        self.automation_worker.status_update.connect(self.on_status_update)
        self.automation_worker.start()
        
    def on_automation_finished(self, results: Dict[str, bool]):
        """Handle automation completion."""
        logger.info("Automation workflow completed")
        
        # Update UI state
        self.control_panel.set_automation_running(False)
        
        # Show results
        self.show_automation_results(results)
        
        # Clean up worker
        if self.automation_worker:
            self.automation_worker.deleteLater()
            self.automation_worker = None
            
    def on_automation_error(self, error_message: str):
        """Handle automation error."""
        logger.error(f"Automation error: {error_message}")
        
        # Update UI state
        self.control_panel.set_automation_running(False)
        
        # Show error message
        QMessageBox.critical(self, "Automation Error", f"Automation failed:\n\n{error_message}")
        
        # Clean up worker
        if self.automation_worker:
            self.automation_worker.deleteLater()
            self.automation_worker = None
            
    def on_status_update(self, status_message: str):
        """Handle status update from automation worker."""
        logger.info(f"Automation status: {status_message}")
        # Could update a status bar here if needed
        
    def show_automation_results(self, results: Dict[str, bool]):
        """Show automation results in a message box."""
        successful_emails = [email for email, success in results.items() if success]
        failed_emails = [email for email, success in results.items() if not success]
        
        message = f"Automation Results:\n\n"
        message += f"Successfully processed: {len(successful_emails)} email(s)\n"
        message += f"Failed to process: {len(failed_emails)} email(s)\n\n"
        
        if successful_emails:
            message += "Successful:\n"
            for email in successful_emails:
                message += f"  ✓ {email}\n"
                
        if failed_emails:
            message += "\nFailed:\n"
            for email in failed_emails:
                message += f"  ✗ {email}\n"
                
        # Choose message box type based on results
        if failed_emails:
            QMessageBox.warning(self, "Automation Results", message)
        else:
            QMessageBox.information(self, "Automation Results", message)
            
    def closeEvent(self, event):
        """Handle window close event."""
        # Stop automation if running
        if self.automation_worker and self.automation_worker.isRunning():
            reply = QMessageBox.question(
                self, 
                "Confirm Exit", 
                "Automation is currently running. Are you sure you want to exit?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.No:
                event.ignore()
                return
                
            # Terminate the worker thread
            self.automation_worker.terminate()
            self.automation_worker.wait(3000)  # Wait up to 3 seconds
            
        event.accept()
