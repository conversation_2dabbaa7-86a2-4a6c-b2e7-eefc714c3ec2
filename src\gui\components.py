"""
GUI Components Module

This module contains reusable GUI components for the FastMode Access Manager.
It provides custom widgets and utility functions for the PyQt6 interface.

Author: BeiGene IT Help Desk Service
Date: 2025-08-28
"""

import webbrowser
from typing import List, Dict, Any, Optional, Callable
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, 
    QPushButton, QLineEdit, QTextEdit, QCheckBox, QMessageBox,
    QProgressBar, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt6.QtGui import QFont, QPalette, QColor
from loguru import logger

from ..utils.config_manager import config_manager
from ..utils.email_utils import validate_and_process_email_input, format_emails_for_display


class URLSelectorWidget(QWidget):
    """
    Widget for URL selection and opening in browser.
    
    Contains a dropdown list of URLs and an Open button to launch
    the selected URL in the default browser.
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_urls()
        
    def setup_ui(self):
        """Set up the UI components for URL selection."""
        layout = QHBoxLayout()
        
        # URL dropdown
        self.url_combo = QComboBox()
        self.url_combo.setMinimumWidth(400)
        layout.addWidget(self.url_combo)
        
        # Open button
        self.open_button = QPushButton("Open")
        self.open_button.setMinimumWidth(80)
        self.open_button.clicked.connect(self.open_selected_url)
        layout.addWidget(self.open_button)
        
        layout.addStretch()
        self.setLayout(layout)
        
    def load_urls(self):
        """Load URLs from configuration and populate the dropdown."""
        try:
            urls_list = config_manager.get_urls_list()
            
            for url_config in urls_list:
                name = url_config.get("name", "Unknown")
                url = url_config.get("url", "")
                description = url_config.get("description", "")
                
                # Store URL in item data
                display_text = f"{name} - {description}" if description else name
                self.url_combo.addItem(display_text, url)
                
            logger.info(f"Loaded {len(urls_list)} URLs into dropdown")
            
        except Exception as e:
            logger.error(f"Failed to load URLs: {str(e)}")
            QMessageBox.warning(self, "Warning", "Failed to load URL configuration")
    
    def open_selected_url(self):
        """Open the selected URL in the default browser."""
        current_index = self.url_combo.currentIndex()
        if current_index >= 0:
            url = self.url_combo.itemData(current_index)
            if url:
                try:
                    webbrowser.open(url)
                    logger.info(f"Opened URL in browser: {url}")
                except Exception as e:
                    logger.error(f"Failed to open URL: {str(e)}")
                    QMessageBox.critical(self, "Error", f"Failed to open URL: {str(e)}")


class EmailInputWidget(QWidget):
    """
    Widget for email input and validation.
    
    Contains an email input field with validation and a Clear button.
    Automatically processes email input and generates dual domain versions.
    """
    
    # Signal emitted when valid emails are generated
    emails_generated = pyqtSignal(list)  # List[str]
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setup_validation()
        
    def setup_ui(self):
        """Set up the UI components for email input."""
        layout = QHBoxLayout()
        
        # Email input field
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("Enter email address (@beigene.com or @beonemed.com)")
        self.email_input.setMinimumWidth(400)
        layout.addWidget(self.email_input)
        
        # Clear button
        self.clear_button = QPushButton("Clear")
        self.clear_button.setMinimumWidth(80)
        self.clear_button.clicked.connect(self.clear_input)
        layout.addWidget(self.clear_button)
        
        layout.addStretch()
        self.setLayout(layout)
        
    def setup_validation(self):
        """Set up email validation and auto-processing."""
        # Connect text changed signal to validation
        self.email_input.textChanged.connect(self.on_text_changed)
        
        # Timer for delayed validation (to avoid validating on every keystroke)
        self.validation_timer = QTimer()
        self.validation_timer.setSingleShot(True)
        self.validation_timer.timeout.connect(self.validate_and_process_email)
        
    def on_text_changed(self, text: str):
        """Handle text change in email input field."""
        # Reset timer on each text change
        self.validation_timer.stop()
        
        # Check if text contains .com to trigger validation
        if ".com" in text.lower():
            self.validation_timer.start(500)  # 500ms delay
            
    def validate_and_process_email(self):
        """Validate and process the email input."""
        email_text = self.email_input.text().strip()
        
        if not email_text:
            self.emails_generated.emit([])
            return
            
        # Validate and process email
        success, email_list, error_message = validate_and_process_email_input(email_text)
        
        if success:
            # Set input field style to indicate success
            self.email_input.setStyleSheet("QLineEdit { border: 2px solid green; }")
            self.emails_generated.emit(email_list)
            logger.info(f"Generated emails: {email_list}")
        else:
            # Set input field style to indicate error
            self.email_input.setStyleSheet("QLineEdit { border: 2px solid red; }")
            self.emails_generated.emit([])
            logger.warning(f"Email validation failed: {error_message}")
            
    def clear_input(self):
        """Clear the email input field."""
        self.email_input.clear()
        self.email_input.setStyleSheet("")  # Reset style
        self.emails_generated.emit([])
        logger.info("Email input cleared")


class EmailDisplayWidget(QWidget):
    """
    Widget for displaying generated email addresses.
    
    Contains a text area that shows the dual domain email addresses
    generated from the input email.
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the UI components for email display."""
        layout = QVBoxLayout()
        
        # Label
        label = QLabel("Generated Email Addresses:")
        label.setFont(QFont("Arial", 9, QFont.Weight.Bold))
        layout.addWidget(label)
        
        # Text area for displaying emails
        self.email_display = QTextEdit()
        self.email_display.setMaximumHeight(100)
        self.email_display.setReadOnly(True)
        self.email_display.setPlaceholderText("Email addresses will appear here...")
        layout.addWidget(self.email_display)
        
        self.setLayout(layout)
        
    def update_emails(self, email_list: List[str]):
        """Update the displayed email addresses."""
        if email_list:
            formatted_text = format_emails_for_display(email_list)
            self.email_display.setPlainText(formatted_text)
        else:
            self.email_display.clear()


class ControlPanelWidget(QWidget):
    """
    Widget for automation control panel.
    
    Contains headless mode toggle, START button, and progress display.
    """
    
    # Signal emitted when START button is clicked
    start_automation = pyqtSignal(list, bool)  # email_list, headless_mode
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_emails = []
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the UI components for control panel."""
        layout = QVBoxLayout()
        
        # Headless mode checkbox
        self.headless_checkbox = QCheckBox("Headless Mode (for debugging, uncheck to see browser)")
        default_headless = config_manager.get_setting("playwright.default_headless", True)
        self.headless_checkbox.setChecked(default_headless)
        layout.addWidget(self.headless_checkbox)
        
        # START button
        self.start_button = QPushButton("START")
        self.start_button.setMinimumHeight(100)
        self.start_button.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        self.start_button.clicked.connect(self.on_start_clicked)
        self.start_button.setEnabled(False)  # Disabled until emails are available
        layout.addWidget(self.start_button)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)
        
        self.setLayout(layout)
        
    def update_emails(self, email_list: List[str]):
        """Update the current email list and enable/disable START button."""
        self.current_emails = email_list
        self.start_button.setEnabled(len(email_list) > 0)
        
        if email_list:
            self.status_label.setText(f"Ready to process {len(email_list)} email(s)")
        else:
            self.status_label.setText("Ready")
            
    def on_start_clicked(self):
        """Handle START button click."""
        if self.current_emails:
            headless_mode = self.headless_checkbox.isChecked()
            self.start_automation.emit(self.current_emails, headless_mode)
            
    def set_automation_running(self, running: bool):
        """Update UI state for automation running/stopped."""
        self.start_button.setEnabled(not running)
        self.progress_bar.setVisible(running)
        
        if running:
            self.status_label.setText("Automation in progress...")
            self.progress_bar.setRange(0, 0)  # Indeterminate progress
        else:
            self.status_label.setText("Ready")
            self.progress_bar.setVisible(False)
