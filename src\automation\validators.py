"""
Validation Functions Module

This module contains validation functions for DOM elements and page states
during Playwright automation operations.

Author: BeiGene IT Help Desk Service
Date: 2025-08-28
"""

from typing import Optional, Dict, Any
from playwright.async_api import Page, Locator
from loguru import logger


async def validate_page_loaded(page: Page, expected_url_pattern: str, timeout: int = 30000) -> bool:
    """
    Validate that the page has loaded and matches expected URL pattern.
    
    Args:
        page (Page): Playwright page object
        expected_url_pattern (str): Expected URL pattern to match
        timeout (int): Timeout in milliseconds
        
    Returns:
        bool: True if page loaded successfully, False otherwise
    """
    try:
        await page.wait_for_load_state('networkidle', timeout=timeout)
        current_url = page.url
        
        if expected_url_pattern in current_url:
            logger.info(f"Page loaded successfully: {current_url}")
            return True
        else:
            logger.error(f"URL mismatch. Expected pattern: {expected_url_pattern}, Got: {current_url}")
            return False
            
    except Exception as e:
        logger.error(f"Page load validation failed: {str(e)}")
        return False


async def validate_element_exists(page: Page, selector: str, timeout: int = 10000) -> bool:
    """
    Validate that a DOM element exists on the page.
    
    Args:
        page (Page): Playwright page object
        selector (str): CSS selector or other locator string
        timeout (int): Timeout in milliseconds
        
    Returns:
        bool: True if element exists, False otherwise
    """
    try:
        element = page.locator(selector)
        await element.wait_for(state='visible', timeout=timeout)
        logger.info(f"Element found: {selector}")
        return True
        
    except Exception as e:
        logger.error(f"Element not found: {selector}, Error: {str(e)}")
        return False


async def validate_element_clickable(page: Page, selector: str, timeout: int = 10000) -> bool:
    """
    Validate that a DOM element is clickable.
    
    Args:
        page (Page): Playwright page object
        selector (str): CSS selector or other locator string
        timeout (int): Timeout in milliseconds
        
    Returns:
        bool: True if element is clickable, False otherwise
    """
    try:
        element = page.locator(selector)
        await element.wait_for(state='visible', timeout=timeout)
        
        # Check if element is enabled
        is_enabled = await element.is_enabled()
        if not is_enabled:
            logger.error(f"Element is not enabled: {selector}")
            return False
            
        logger.info(f"Element is clickable: {selector}")
        return True
        
    except Exception as e:
        logger.error(f"Element clickability validation failed: {selector}, Error: {str(e)}")
        return False


async def validate_input_field(page: Page, selector: str, timeout: int = 10000) -> bool:
    """
    Validate that an input field is ready for text input.
    
    Args:
        page (Page): Playwright page object
        selector (str): CSS selector for input field
        timeout (int): Timeout in milliseconds
        
    Returns:
        bool: True if input field is ready, False otherwise
    """
    try:
        element = page.locator(selector)
        await element.wait_for(state='visible', timeout=timeout)
        
        # Check if element is enabled and editable
        is_enabled = await element.is_enabled()
        is_editable = await element.is_editable()
        
        if not is_enabled:
            logger.error(f"Input field is not enabled: {selector}")
            return False
            
        if not is_editable:
            logger.error(f"Input field is not editable: {selector}")
            return False
            
        logger.info(f"Input field is ready: {selector}")
        return True
        
    except Exception as e:
        logger.error(f"Input field validation failed: {selector}, Error: {str(e)}")
        return False


async def validate_modal_dialog(page: Page, modal_selector: str, timeout: int = 10000) -> bool:
    """
    Validate that a modal dialog has appeared on the page.
    
    Args:
        page (Page): Playwright page object
        modal_selector (str): CSS selector for modal dialog
        timeout (int): Timeout in milliseconds
        
    Returns:
        bool: True if modal is visible, False otherwise
    """
    try:
        modal = page.locator(modal_selector)
        await modal.wait_for(state='visible', timeout=timeout)
        
        # Additional check for modal visibility
        is_visible = await modal.is_visible()
        if is_visible:
            logger.info(f"Modal dialog is visible: {modal_selector}")
            return True
        else:
            logger.error(f"Modal dialog is not visible: {modal_selector}")
            return False
            
    except Exception as e:
        logger.error(f"Modal dialog validation failed: {modal_selector}, Error: {str(e)}")
        return False


async def validate_switch_state(page: Page, switch_selector: str, expected_state: bool, timeout: int = 10000) -> bool:
    """
    Validate the state of a toggle switch element.
    
    Args:
        page (Page): Playwright page object
        switch_selector (str): CSS selector for switch element
        expected_state (bool): Expected state (True for on, False for off)
        timeout (int): Timeout in milliseconds
        
    Returns:
        bool: True if switch is in expected state, False otherwise
    """
    try:
        switch_element = page.locator(switch_selector)
        await switch_element.wait_for(state='visible', timeout=timeout)
        
        # Check aria-checked attribute
        aria_checked = await switch_element.get_attribute('aria-checked')
        current_state = aria_checked == 'true'
        
        if current_state == expected_state:
            logger.info(f"Switch is in expected state ({expected_state}): {switch_selector}")
            return True
        else:
            logger.warning(f"Switch state mismatch. Expected: {expected_state}, Current: {current_state}")
            return False
            
    except Exception as e:
        logger.error(f"Switch state validation failed: {switch_selector}, Error: {str(e)}")
        return False
