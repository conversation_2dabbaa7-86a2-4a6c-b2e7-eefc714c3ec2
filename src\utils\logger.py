"""
Logger Configuration Module

This module sets up and configures the logging system for the application.
It uses loguru for enhanced logging capabilities with file rotation and
structured logging.

Author: BeiGene IT Help Desk Service
Date: 2025-08-28
"""

import os
import sys
from pathlib import Path
from loguru import logger
from typing import Optional

from .config_manager import config_manager


def setup_logger(log_level: Optional[str] = None) -> None:
    """
    Configure the application logger with file and console output.
    
    This function sets up loguru logger with:
    - Console output with colored formatting
    - File output with rotation and retention
    - Configurable log levels
    
    Args:
        log_level (Optional[str]): Override log level from config
    """
    # Remove default logger
    logger.remove()
    
    # Load logging configuration
    log_config = config_manager.get_setting("logging", {})
    level = log_level or log_config.get("level", "INFO")
    log_file = log_config.get("file_path", "logs/app.log")
    max_size = log_config.get("max_file_size", "10MB")
    backup_count = log_config.get("backup_count", 5)
    
    # Ensure logs directory exists
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Console logger with colors
    logger.add(
        sys.stdout,
        level=level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True
    )
    
    # File logger with rotation
    logger.add(
        log_file,
        level=level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation=max_size,
        retention=backup_count,
        compression="zip",
        encoding="utf-8"
    )
    
    logger.info(f"Logger initialized with level: {level}")
    logger.info(f"Log file: {log_file}")


def get_logger(name: str):
    """
    Get a logger instance for a specific module.
    
    Args:
        name (str): Name of the module requesting the logger
        
    Returns:
        Logger: Configured logger instance
    """
    return logger.bind(name=name)


# Initialize logger when module is imported
setup_logger()
