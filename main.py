"""
FastMode Access Manager - Main Entry Point

This is the main entry point for the BeiGene IT Help Desk Service automation tool.
It initializes the PyQt6 application and launches the main window.

Author: BeiGene IT Help Desk Service
Date: 2025-08-28
"""

import sys
import os
import asyncio
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt
from loguru import logger

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.gui.main_window import MainWindow
from src.utils.config_manager import config_manager
from src.utils.logger import setup_logger


def setup_application_environment():
    """
    Set up the application environment and dependencies.
    
    This function ensures all necessary directories exist and
    dependencies are properly configured.
    """
    try:
        # Ensure logs directory exists
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        
        # Ensure config directory exists
        config_dir = Path("config")
        if not config_dir.exists():
            logger.error("Configuration directory not found. Please ensure config/ directory exists.")
            return False
            
        # Validate configuration files
        required_configs = ["settings.json", "urls.json"]
        for config_file in required_configs:
            config_path = config_dir / config_file
            if not config_path.exists():
                logger.error(f"Required configuration file not found: {config_path}")
                return False
                
        logger.info("Application environment setup completed")
        return True
        
    except Exception as e:
        logger.error(f"Failed to setup application environment: {str(e)}")
        return False


def check_dependencies():
    """
    Check if all required dependencies are installed.
    
    Returns:
        bool: True if all dependencies are available, False otherwise
    """
    required_modules = [
        ("PyQt6", "PyQt6"),
        ("playwright", "playwright"),
        ("loguru", "loguru"),
        ("email_validator", "email-validator"),
        ("pydantic", "pydantic")
    ]
    
    missing_modules = []
    
    for module_name, package_name in required_modules:
        try:
            __import__(module_name)
        except ImportError:
            missing_modules.append(package_name)
            
    if missing_modules:
        logger.error(f"Missing required dependencies: {', '.join(missing_modules)}")
        logger.error("Please install missing dependencies using: pip install -r requirements.txt")
        return False
        
    logger.info("All required dependencies are available")
    return True


def install_playwright_browsers():
    """
    Check if Playwright browsers are installed and prompt user if needed.
    
    Returns:
        bool: True if browsers are available or user chooses to continue, False otherwise
    """
    try:
        from playwright.sync_api import sync_playwright
        
        # Try to launch a browser to check if it's installed
        with sync_playwright() as p:
            try:
                browser = p.chromium.launch(headless=True)
                browser.close()
                logger.info("Playwright browsers are available")
                return True
            except Exception as e:
                logger.warning(f"Playwright browsers may not be installed: {str(e)}")
                
                # Show message to user about installing browsers
                app = QApplication.instance()
                if app is None:
                    # Create temporary app for message box
                    temp_app = QApplication([])
                    
                reply = QMessageBox.question(
                    None,
                    "Playwright Browsers Required",
                    "Playwright browsers are not installed.\n\n"
                    "To install them, run the following command in your terminal:\n"
                    "playwright install\n\n"
                    "Do you want to continue anyway? (The application may not work properly)",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No
                )
                
                if app is None:
                    temp_app.quit()
                    
                return reply == QMessageBox.StandardButton.Yes
                
    except ImportError:
        logger.error("Playwright is not installed")
        return False


def main():
    """
    Main application entry point.
    
    This function initializes the application, sets up the environment,
    and launches the main window.
    """
    try:
        # Initialize logging
        setup_logger()
        logger.info("Starting FastMode Access Manager")
        
        # Check dependencies
        if not check_dependencies():
            sys.exit(1)
            
        # Set up application environment
        if not setup_application_environment():
            sys.exit(1)
            
        # Create PyQt6 application
        app = QApplication(sys.argv)
        
        # Set application properties
        app_config = config_manager.get_setting("application", {})
        app.setApplicationName(app_config.get("name", "FastMode Access Manager"))
        app.setApplicationVersion(app_config.get("version", "1.0.0"))
        
        # Set application style
        app.setStyle("Fusion")  # Use Fusion style for consistent appearance
        
        # Check Playwright browsers (optional check)
        if not install_playwright_browsers():
            logger.warning("Continuing without Playwright browser verification")
            
        # Create and show main window
        main_window = MainWindow()
        main_window.show()
        
        logger.info("Application started successfully")
        
        # Start the application event loop
        sys.exit(app.exec())
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        sys.exit(0)
        
    except Exception as e:
        logger.error(f"Fatal error: {str(e)}")
        
        # Try to show error message if possible
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
                
            QMessageBox.critical(
                None,
                "Fatal Error",
                f"A fatal error occurred:\n\n{str(e)}\n\nPlease check the logs for more details."
            )
        except:
            pass  # If we can't show the message box, just exit
            
        sys.exit(1)


if __name__ == "__main__":
    main()
