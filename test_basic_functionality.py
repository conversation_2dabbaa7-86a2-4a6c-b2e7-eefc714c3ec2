"""
Basic Functionality Test Script

This script tests the core functionality of the FastMode Access Manager
without requiring the full GUI or Playwright automation.

Author: BeiGene IT Help Desk Service
Date: 2025-08-28
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_configuration_loading():
    """Test configuration loading functionality."""
    print("Testing configuration loading...")
    
    try:
        from src.utils.config_manager import config_manager
        
        # Test settings loading
        settings = config_manager.load_settings()
        print(f"✓ Settings loaded: {len(settings)} top-level keys")
        
        # Test URLs loading
        urls = config_manager.load_urls()
        print(f"✓ URLs loaded: {len(urls.get('urls', []))} URLs configured")
        
        # Test specific setting retrieval
        app_name = config_manager.get_setting("application.name", "Unknown")
        print(f"✓ Application name: {app_name}")
        
        # Test email domains
        domains = config_manager.get_email_domains()
        print(f"✓ Email domains: {domains}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration loading failed: {str(e)}")
        return False


def test_email_utilities():
    """Test email utility functions."""
    print("\nTesting email utilities...")
    
    try:
        from src.utils.email_utils import (
            validate_email_address,
            is_supported_domain,
            extract_username_from_email,
            generate_dual_domain_emails,
            validate_and_process_email_input
        )
        
        # Test email validation
        test_emails = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>",
            "not-an-email"
        ]
        
        for email in test_emails:
            is_valid = validate_email_address(email)
            is_supported = is_supported_domain(email) if is_valid else False
            print(f"  {email}: valid={is_valid}, supported={is_supported}")
            
        # Test username extraction
        username = extract_username_from_email("<EMAIL>")
        print(f"✓ Username extraction: {username}")
        
        # Test dual domain generation
        dual_emails = generate_dual_domain_emails("<EMAIL>")
        print(f"✓ Dual domain emails: {dual_emails}")
        
        # Test complete processing
        success, emails, error = validate_and_process_email_input("<EMAIL>")
        print(f"✓ Complete processing: success={success}, emails={emails}")
        
        return True
        
    except Exception as e:
        print(f"✗ Email utilities test failed: {str(e)}")
        return False


def test_dom_selectors():
    """Test DOM selector configuration."""
    print("\nTesting DOM selectors...")
    
    try:
        from src.automation.operations import DOM_SELECTORS
        
        required_selectors = [
            "new_button",
            "username_input", 
            "realname_input",
            "email_input",
            "aad_switch",
            "ok_button",
            "modal_dialog"
        ]
        
        for selector_name in required_selectors:
            if selector_name in DOM_SELECTORS:
                selector = DOM_SELECTORS[selector_name]
                print(f"✓ {selector_name}: {selector[:50]}...")
            else:
                print(f"✗ Missing selector: {selector_name}")
                return False
                
        return True
        
    except Exception as e:
        print(f"✗ DOM selectors test failed: {str(e)}")
        return False


def test_playwright_manager_initialization():
    """Test Playwright manager initialization (without actually starting browser)."""
    print("\nTesting Playwright manager initialization...")
    
    try:
        from src.automation.playwright_manager import PlaywrightManager
        
        # Create manager instance
        manager = PlaywrightManager()
        print("✓ PlaywrightManager instance created")
        
        # Check initial state
        is_ready = manager.is_ready()
        print(f"✓ Initial ready state: {is_ready} (should be False)")
        
        # Check configuration loading
        config = manager.config
        print(f"✓ Configuration loaded: {len(config)} settings")
        
        return True
        
    except Exception as e:
        print(f"✗ Playwright manager test failed: {str(e)}")
        return False


def test_logging_setup():
    """Test logging configuration."""
    print("\nTesting logging setup...")
    
    try:
        from loguru import logger
        
        # Test basic logging
        logger.info("Test log message")
        print("✓ Basic logging works")
        
        # Check if log file exists
        log_file = Path("logs/app.log")
        if log_file.exists():
            print(f"✓ Log file exists: {log_file}")
            
            # Check log file size
            size = log_file.stat().st_size
            print(f"✓ Log file size: {size} bytes")
        else:
            print("! Log file not found (may be created on first use)")
            
        return True
        
    except Exception as e:
        print(f"✗ Logging test failed: {str(e)}")
        return False


def test_gui_components_import():
    """Test GUI components can be imported."""
    print("\nTesting GUI components import...")
    
    try:
        # Test PyQt6 availability
        from PyQt6.QtWidgets import QApplication
        print("✓ PyQt6 available")
        
        # Test custom components
        from src.gui.components import (
            URLSelectorWidget,
            EmailInputWidget, 
            EmailDisplayWidget,
            ControlPanelWidget
        )
        print("✓ GUI components can be imported")
        
        # Test main window
        from src.gui.main_window import MainWindow, AutomationWorker
        print("✓ Main window components can be imported")
        
        return True
        
    except Exception as e:
        print(f"✗ GUI components test failed: {str(e)}")
        return False


def run_all_tests():
    """Run all basic functionality tests."""
    print("=" * 60)
    print("FastMode Access Manager - Basic Functionality Tests")
    print("=" * 60)
    
    tests = [
        ("Configuration Loading", test_configuration_loading),
        ("Email Utilities", test_email_utilities),
        ("DOM Selectors", test_dom_selectors),
        ("Playwright Manager", test_playwright_manager_initialization),
        ("Logging Setup", test_logging_setup),
        ("GUI Components", test_gui_components_import)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application is ready to use.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
