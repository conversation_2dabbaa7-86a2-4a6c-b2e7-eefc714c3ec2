2025-08-28 15:03:22 | INFO     | src.utils.logger:setup_logger:69 | Logger initialized with level: INFO
2025-08-28 15:03:22 | INFO     | src.utils.logger:setup_logger:70 | Log file: logs/app.log
2025-08-28 15:03:22 | INFO     | src.utils.logger:setup_logger:69 | Logger initialized with level: INFO
2025-08-28 15:03:22 | INFO     | src.utils.logger:setup_logger:70 | Log file: logs/app.log
2025-08-28 15:03:22 | INFO     | __main__:main:150 | Starting FastMode Access Manager
2025-08-28 15:03:22 | INFO     | __main__:check_dependencies:89 | All required dependencies are available
2025-08-28 15:03:22 | INFO     | __main__:setup_application_environment:53 | Application environment setup completed
2025-08-28 15:03:36 | INFO     | __main__:install_playwright_browsers:108 | Playwright browsers are available
2025-08-28 15:03:36 | INFO     | src.utils.config_manager:load_urls:80 | URLs loaded from config\urls.json
2025-08-28 15:03:36 | INFO     | src.gui.components:load_urls:71 | Loaded 3 URLs into dropdown
2025-08-28 15:03:37 | INFO     | __main__:main:179 | Application started successfully
2025-08-28 15:08:19 | INFO     | src.utils.logger:setup_logger:69 | Logger initialized with level: INFO
2025-08-28 15:08:19 | INFO     | src.utils.logger:setup_logger:70 | Log file: logs/app.log
2025-08-28 15:08:19 | INFO     | src.utils.logger:setup_logger:69 | Logger initialized with level: INFO
2025-08-28 15:08:19 | INFO     | src.utils.logger:setup_logger:70 | Log file: logs/app.log
2025-08-28 15:08:19 | INFO     | __main__:main:150 | Starting FastMode Access Manager
2025-08-28 15:08:19 | INFO     | __main__:check_dependencies:89 | All required dependencies are available
2025-08-28 15:08:19 | INFO     | __main__:setup_application_environment:53 | Application environment setup completed
2025-08-28 15:08:22 | INFO     | __main__:install_playwright_browsers:108 | Playwright browsers are available
2025-08-28 15:08:22 | INFO     | src.utils.config_manager:load_urls:80 | URLs loaded from config\urls.json
2025-08-28 15:08:22 | INFO     | src.gui.components:load_urls:71 | Loaded 3 URLs into dropdown
2025-08-28 15:08:22 | INFO     | __main__:main:179 | Application started successfully
2025-08-28 15:08:30 | INFO     | src.gui.components:open_selected_url:85 | Opened URL in browser: https://rdai.beigenecorp.net/admin/#/admin/organization/user
2025-08-28 15:08:55 | INFO     | src.utils.email_utils:generate_dual_domain_emails:105 | Generated dual domain emails for lifu.lifu: ['<EMAIL>', '<EMAIL>']
2025-08-28 15:08:55 | INFO     | src.gui.components:validate_and_process_email:160 | Generated emails: ['<EMAIL>', '<EMAIL>']
2025-08-28 15:27:28 | INFO     | src.gui.main_window:start_automation:227 | Starting automation for 2 emails, headless: True
2025-08-28 15:27:28 | INFO     | src.automation.playwright_manager:initialize_full_session:191 | Executing: Starting Playwright
2025-08-28 15:27:28 | INFO     | src.gui.main_window:on_status_update:276 | Automation status: Initializing Playwright...
2025-08-28 15:27:30 | INFO     | src.automation.playwright_manager:start_playwright:43 | Playwright started successfully
2025-08-28 15:27:30 | INFO     | src.automation.playwright_manager:initialize_full_session:191 | Executing: Launching browser
2025-08-28 15:27:31 | INFO     | src.automation.playwright_manager:launch_browser:92 | Browser launched successfully: chromium (headless: True)
2025-08-28 15:27:31 | INFO     | src.automation.playwright_manager:initialize_full_session:191 | Executing: Creating context
2025-08-28 15:27:31 | INFO     | src.automation.playwright_manager:create_context:118 | Browser context created with viewport: {'width': 1920, 'height': 1080}
2025-08-28 15:27:31 | INFO     | src.automation.playwright_manager:initialize_full_session:191 | Executing: Creating page
2025-08-28 15:27:32 | INFO     | src.automation.playwright_manager:create_page:143 | Page created successfully with timeout: 30000ms
2025-08-28 15:27:32 | INFO     | src.automation.playwright_manager:initialize_full_session:197 | Playwright session initialized successfully
2025-08-28 15:27:32 | INFO     | src.automation.operations:execute_complete_user_creation_workflow:472 | Starting complete workflow for 2 email addresses
2025-08-28 15:27:32 | INFO     | src.gui.main_window:on_status_update:276 | Automation status: Starting user creation workflow...
2025-08-28 15:27:32 | INFO     | src.automation.operations:execute_complete_user_creation_workflow:477 | Processing email: <EMAIL>
2025-08-28 15:27:32 | INFO     | src.automation.operations:execute_operation_group_i:382 | Starting Operation Group I for: <EMAIL>
2025-08-28 15:27:32 | INFO     | src.automation.operations:navigate_to_admin_portal:55 | Step 1: Navigating to admin portal: https://rdai.beigenecorp.net/admin/#/admin/organization/user
2025-08-28 15:27:37 | INFO     | src.automation.validators:validate_page_loaded:33 | Page loaded successfully: https://rdai.beigenecorp.net/admin/#/admin/organization/user
2025-08-28 15:27:37 | INFO     | src.automation.operations:navigate_to_admin_portal:66 | Successfully navigated to admin portal
2025-08-28 15:27:37 | INFO     | src.automation.operations:click_new_button:90 | Step 2: Clicking New button
2025-08-28 15:27:47 | ERROR    | src.automation.validators:validate_element_clickable:93 | Element clickability validation failed: button.ant-btn.css-iyixax.ant-btn-primary.ant-btn-color-primary.ant-btn-variant-solid, Error: Locator.wait_for: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("button.ant-btn.css-iyixax.ant-btn-primary.ant-btn-color-primary.ant-btn-variant-solid") to be visible
  -     2 × waiting for" https://login.microsoftonline.com/7dbc552d-50d7-4396-aeb9-04d0d393261b/oauth2/v2.0/authorize?client_id=a3268362-f50f-4d17-b94c-eb3ec966438c&scope=user.read%20openid%20profile%20offline_access&redirec…" navigation to finish...
  -       - navigated to "https://login.microsoftonline.com/7dbc552d-50d7-4396-aeb9-04d0d393261b/oauth2/v2.0/authorize?client_id=a3268362-f50f-4d17-b94c-eb3ec966438c&scope=user.read%20openid%20profile%20offline_access&redirec…"

2025-08-28 15:27:47 | ERROR    | src.automation.operations:click_new_button:97 | New button is not clickable
2025-08-28 15:27:47 | ERROR    | src.automation.operations:execute_operation_group_i:395 | Operation Group I failed at New button step
2025-08-28 15:27:48 | INFO     | src.automation.operations:execute_complete_user_creation_workflow:477 | Processing email: <EMAIL>
2025-08-28 15:27:48 | INFO     | src.automation.operations:execute_operation_group_ii:425 | Starting Operation Group II for: <EMAIL>
2025-08-28 15:27:48 | INFO     | src.automation.operations:navigate_to_admin_portal:55 | Step 1: Navigating to admin portal: https://rdai.beigenecorp.net/admin/#/admin/organization/user
2025-08-28 15:27:49 | INFO     | src.automation.validators:validate_page_loaded:33 | Page loaded successfully: https://rdai.beigenecorp.net/admin/#/admin/organization/user
2025-08-28 15:27:49 | INFO     | src.automation.operations:navigate_to_admin_portal:66 | Successfully navigated to admin portal
2025-08-28 15:27:49 | INFO     | src.automation.operations:click_new_button:90 | Step 2: Clicking New button
2025-08-28 15:27:59 | ERROR    | src.automation.validators:validate_element_clickable:93 | Element clickability validation failed: button.ant-btn.css-iyixax.ant-btn-primary.ant-btn-color-primary.ant-btn-variant-solid, Error: Locator.wait_for: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("button.ant-btn.css-iyixax.ant-btn-primary.ant-btn-color-primary.ant-btn-variant-solid") to be visible
  -     - waiting for" https://login.microsoftonline.com/7dbc552d-50d7-4396-aeb9-04d0d393261b/oauth2/v2.0/authorize?client_id=a3268362-f50f-4d17-b94c-eb3ec966438c&scope=user.read%20openid%20profile%20offline_access&redirec…" navigation to finish...
  -     - navigated to "https://login.microsoftonline.com/7dbc552d-50d7-4396-aeb9-04d0d393261b/oauth2/v2.0/authorize?client_id=a3268362-f50f-4d17-b94c-eb3ec966438c&scope=user.read%20openid%20profile%20offline_access&redirec…"

2025-08-28 15:27:59 | ERROR    | src.automation.operations:click_new_button:97 | New button is not clickable
2025-08-28 15:27:59 | ERROR    | src.automation.operations:execute_operation_group_ii:442 | Operation Group II failed at New button step
2025-08-28 15:28:00 | INFO     | src.automation.operations:execute_complete_user_creation_workflow:505 | Workflow completed: 0/2 emails processed successfully
2025-08-28 15:28:00 | INFO     | src.gui.main_window:on_status_update:276 | Automation status: Automation completed
2025-08-28 15:28:00 | INFO     | src.automation.playwright_manager:cleanup:206 | Page closed
2025-08-28 15:28:00 | INFO     | src.automation.playwright_manager:cleanup:211 | Context closed
2025-08-28 15:28:00 | INFO     | src.automation.playwright_manager:cleanup:216 | Browser closed
2025-08-28 15:28:00 | INFO     | src.automation.playwright_manager:cleanup:221 | Playwright stopped
2025-08-28 15:28:00 | INFO     | src.gui.main_window:on_automation_finished:246 | Automation workflow completed
2025-08-28 15:38:59 | INFO     | src.utils.logger:setup_logger:69 | Logger initialized with level: INFO
2025-08-28 15:38:59 | INFO     | src.utils.logger:setup_logger:70 | Log file: logs/app.log
2025-08-28 15:38:59 | INFO     | src.utils.logger:setup_logger:69 | Logger initialized with level: INFO
2025-08-28 15:38:59 | INFO     | src.utils.logger:setup_logger:70 | Log file: logs/app.log
2025-08-28 15:38:59 | INFO     | __main__:main:150 | Starting FastMode Access Manager
2025-08-28 15:38:59 | INFO     | __main__:check_dependencies:89 | All required dependencies are available
2025-08-28 15:38:59 | INFO     | __main__:setup_application_environment:53 | Application environment setup completed
2025-08-28 15:39:02 | INFO     | __main__:install_playwright_browsers:108 | Playwright browsers are available
2025-08-28 15:39:02 | INFO     | src.utils.config_manager:load_urls:80 | URLs loaded from config\urls.json
2025-08-28 15:39:02 | INFO     | src.gui.components:load_urls:71 | Loaded 3 URLs into dropdown
2025-08-28 15:39:02 | INFO     | __main__:main:179 | Application started successfully
2025-08-28 15:39:16 | INFO     | src.utils.email_utils:generate_dual_domain_emails:105 | Generated dual domain emails for lifu.lifu: ['<EMAIL>', '<EMAIL>']
2025-08-28 15:39:16 | INFO     | src.gui.components:validate_and_process_email:160 | Generated emails: ['<EMAIL>', '<EMAIL>']
2025-08-28 15:39:17 | INFO     | src.gui.main_window:start_automation:244 | Starting automation for 2 emails, headless: True
2025-08-28 15:39:17 | INFO     | src.automation.playwright_manager:initialize_full_session:191 | Executing: Starting Playwright
2025-08-28 15:39:17 | INFO     | src.gui.main_window:on_status_update:293 | Automation status: Initializing Playwright...
2025-08-28 15:39:18 | INFO     | src.automation.playwright_manager:start_playwright:43 | Playwright started successfully
2025-08-28 15:39:18 | INFO     | src.automation.playwright_manager:initialize_full_session:191 | Executing: Launching browser
2025-08-28 15:39:18 | INFO     | src.automation.playwright_manager:launch_browser:92 | Browser launched successfully: chromium (headless: True)
2025-08-28 15:39:18 | INFO     | src.automation.playwright_manager:initialize_full_session:191 | Executing: Creating context
2025-08-28 15:39:19 | INFO     | src.automation.playwright_manager:create_context:118 | Browser context created with viewport: {'width': 1920, 'height': 1080}
2025-08-28 15:39:19 | INFO     | src.automation.playwright_manager:initialize_full_session:191 | Executing: Creating page
2025-08-28 15:39:19 | INFO     | src.automation.playwright_manager:create_page:143 | Page created successfully with timeout: 30000ms
2025-08-28 15:39:19 | INFO     | src.automation.playwright_manager:initialize_full_session:197 | Playwright session initialized successfully
2025-08-28 15:39:19 | INFO     | src.gui.main_window:on_status_update:293 | Automation status: Checking authentication...
2025-08-28 15:39:24 | INFO     | src.automation.auth_handler:is_authentication_required:52 | Checking authentication for URL: https://rdai.beigenecorp.net/admin/#/admin/organization/user
2025-08-28 15:39:24 | INFO     | src.automation.auth_handler:is_authentication_required:63 | Already authenticated - on admin portal
2025-08-28 15:39:24 | INFO     | src.automation.operations:execute_complete_user_creation_workflow:539 | Starting complete workflow for 2 email addresses
2025-08-28 15:39:24 | INFO     | src.gui.main_window:on_status_update:293 | Automation status: Starting user creation workflow...
2025-08-28 15:39:24 | INFO     | src.automation.operations:execute_complete_user_creation_workflow:544 | Processing email: <EMAIL>
2025-08-28 15:39:24 | INFO     | src.automation.operations:execute_operation_group_i:449 | Starting Operation Group I for: <EMAIL>
2025-08-28 15:39:24 | INFO     | src.automation.operations:navigate_to_admin_portal:99 | Step 1: Navigating to admin portal: https://rdai.beigenecorp.net/admin/#/admin/organization/user
2025-08-28 15:39:24 | INFO     | src.automation.auth_handler:is_authentication_required:52 | Checking authentication for URL: https://rdai.beigenecorp.net/admin/#/admin/organization/user
2025-08-28 15:39:24 | INFO     | src.automation.auth_handler:is_authentication_required:63 | Already authenticated - on admin portal
2025-08-28 15:39:24 | INFO     | src.automation.auth_handler:verify_admin_portal_access:180 | Verifying admin portal access: https://rdai.beigenecorp.net/admin/#/admin/organization/user
2025-08-28 15:39:24 | INFO     | src.automation.auth_handler:verify_admin_portal_access:185 | Admin portal access verified: rdai.beigenecorp.net/admin
2025-08-28 15:39:25 | INFO     | src.automation.auth_handler:verify_admin_portal_access:205 | Admin portal access verified by URL
2025-08-28 15:39:25 | INFO     | src.automation.operations:navigate_to_admin_portal:119 | Successfully navigated to admin portal
2025-08-28 15:39:25 | INFO     | src.automation.operations:click_new_button:143 | Step 2: Clicking New button
2025-08-28 15:40:15 | ERROR    | src.automation.validators:validate_element_clickable:101 | No clickable element found with any of the provided selectors
2025-08-28 15:40:15 | ERROR    | src.automation.operations:click_new_button:151 | New button is not clickable with any selector
2025-08-28 15:40:15 | ERROR    | src.automation.operations:execute_operation_group_i:462 | Operation Group I failed at New button step
2025-08-28 15:40:16 | INFO     | src.automation.operations:execute_complete_user_creation_workflow:544 | Processing email: <EMAIL>
2025-08-28 15:40:16 | INFO     | src.automation.operations:execute_operation_group_ii:492 | Starting Operation Group II for: <EMAIL>
2025-08-28 15:40:16 | INFO     | src.automation.operations:navigate_to_admin_portal:99 | Step 1: Navigating to admin portal: https://rdai.beigenecorp.net/admin/#/admin/organization/user
2025-08-28 15:40:17 | INFO     | src.automation.auth_handler:is_authentication_required:52 | Checking authentication for URL: https://rdai.beigenecorp.net/admin/#/admin/organization/user
2025-08-28 15:40:17 | INFO     | src.automation.auth_handler:is_authentication_required:63 | Already authenticated - on admin portal
2025-08-28 15:40:17 | INFO     | src.automation.auth_handler:verify_admin_portal_access:180 | Verifying admin portal access: https://rdai.beigenecorp.net/admin/#/admin/organization/user
2025-08-28 15:40:17 | INFO     | src.automation.auth_handler:verify_admin_portal_access:185 | Admin portal access verified: rdai.beigenecorp.net/admin
2025-08-28 15:40:17 | INFO     | src.automation.auth_handler:verify_admin_portal_access:205 | Admin portal access verified by URL
2025-08-28 15:40:17 | INFO     | src.automation.operations:navigate_to_admin_portal:119 | Successfully navigated to admin portal
2025-08-28 15:40:17 | INFO     | src.automation.operations:click_new_button:143 | Step 2: Clicking New button
2025-08-28 15:41:07 | ERROR    | src.automation.validators:validate_element_clickable:101 | No clickable element found with any of the provided selectors
2025-08-28 15:41:07 | ERROR    | src.automation.operations:click_new_button:151 | New button is not clickable with any selector
2025-08-28 15:41:07 | ERROR    | src.automation.operations:execute_operation_group_ii:509 | Operation Group II failed at New button step
2025-08-28 15:41:08 | INFO     | src.automation.operations:execute_complete_user_creation_workflow:572 | Workflow completed: 0/2 emails processed successfully
2025-08-28 15:41:08 | INFO     | src.gui.main_window:on_status_update:293 | Automation status: Automation completed
2025-08-28 15:41:08 | INFO     | src.automation.playwright_manager:cleanup:206 | Page closed
2025-08-28 15:41:08 | INFO     | src.automation.playwright_manager:cleanup:211 | Context closed
2025-08-28 15:41:09 | INFO     | src.automation.playwright_manager:cleanup:216 | Browser closed
2025-08-28 15:41:09 | INFO     | src.automation.playwright_manager:cleanup:221 | Playwright stopped
2025-08-28 15:41:09 | INFO     | src.gui.main_window:on_automation_finished:263 | Automation workflow completed
