"""
Configuration Manager Module

This module handles loading and managing configuration settings from JSON files.
It provides a centralized way to access application settings, URLs, and other
configurable parameters.

Author: BeiGene IT Help Desk Service
Date: 2025-08-28
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, List, Optional
from loguru import logger


class ConfigManager:
    """
    Manages application configuration settings from JSON files.
    
    This class provides methods to load and access configuration data
    from various JSON configuration files in the config directory.
    """
    
    def __init__(self, config_dir: str = "config"):
        """
        Initialize the configuration manager.
        
        Args:
            config_dir (str): Path to the configuration directory
        """
        self.config_dir = Path(config_dir)
        self._settings: Optional[Dict[str, Any]] = None
        self._urls: Optional[Dict[str, Any]] = None
        
    def load_settings(self) -> Dict[str, Any]:
        """
        Load application settings from settings.json file.
        
        Returns:
            Dict[str, Any]: Dictionary containing application settings
            
        Raises:
            FileNotFoundError: If settings.json file is not found
            json.JSONDecodeError: If JSON file is malformed
        """
        if self._settings is None:
            settings_path = self.config_dir / "settings.json"
            try:
                with open(settings_path, 'r', encoding='utf-8') as file:
                    self._settings = json.load(file)
                logger.info(f"Settings loaded from {settings_path}")
            except FileNotFoundError:
                logger.error(f"Settings file not found: {settings_path}")
                raise
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in settings file: {e}")
                raise
                
        return self._settings
    
    def load_urls(self) -> Dict[str, Any]:
        """
        Load URL configurations from urls.json file.
        
        Returns:
            Dict[str, Any]: Dictionary containing URL configurations
            
        Raises:
            FileNotFoundError: If urls.json file is not found
            json.JSONDecodeError: If JSON file is malformed
        """
        if self._urls is None:
            urls_path = self.config_dir / "urls.json"
            try:
                with open(urls_path, 'r', encoding='utf-8') as file:
                    self._urls = json.load(file)
                logger.info(f"URLs loaded from {urls_path}")
            except FileNotFoundError:
                logger.error(f"URLs file not found: {urls_path}")
                raise
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in URLs file: {e}")
                raise
                
        return self._urls
    
    def get_setting(self, key_path: str, default: Any = None) -> Any:
        """
        Get a specific setting value using dot notation.
        
        Args:
            key_path (str): Dot-separated path to the setting (e.g., "gui.window.width")
            default (Any): Default value if setting is not found
            
        Returns:
            Any: The setting value or default if not found
        """
        settings = self.load_settings()
        keys = key_path.split('.')
        
        current = settings
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                logger.warning(f"Setting not found: {key_path}, using default: {default}")
                return default
                
        return current
    
    def get_urls_list(self) -> List[Dict[str, str]]:
        """
        Get the list of configured URLs.
        
        Returns:
            List[Dict[str, str]]: List of URL configurations with name, url, and description
        """
        urls_config = self.load_urls()
        return urls_config.get('urls', [])
    
    def get_email_domains(self) -> List[str]:
        """
        Get the list of supported email domains.
        
        Returns:
            List[str]: List of email domains (e.g., ["@beigene.com", "@beonemed.com"])
        """
        return self.get_setting("gui.email_domains", ["@beigene.com", "@beonemed.com"])
    
    def get_playwright_config(self) -> Dict[str, Any]:
        """
        Get Playwright configuration settings.
        
        Returns:
            Dict[str, Any]: Playwright configuration parameters
        """
        return self.get_setting("playwright", {
            "default_headless": True,
            "timeout": 30000,
            "browser": "chromium",
            "viewport": {"width": 1920, "height": 1080}
        })


# Global configuration manager instance
config_manager = ConfigManager()
