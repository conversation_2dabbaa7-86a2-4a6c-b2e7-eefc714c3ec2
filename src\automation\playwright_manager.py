"""
Playwright Manager Module

This module manages Playwright browser instances and provides a centralized
interface for browser automation operations.

Author: BeiGene IT Help Desk Service
Date: 2025-08-28
"""

from typing import Optional, Dict, Any
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Playwright
from loguru import logger

from ..utils.config_manager import config_manager


class PlaywrightManager:
    """
    Manages Playwright browser instances and automation operations.
    
    This class provides a centralized way to manage browser instances,
    contexts, and pages for web automation tasks.
    """
    
    def __init__(self):
        """Initialize the Playwright manager."""
        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.config = config_manager.get_playwright_config()
        
    async def start_playwright(self) -> bool:
        """
        Start Playwright and initialize browser instance.
        
        Returns:
            bool: True if started successfully, False otherwise
        """
        try:
            self.playwright = await async_playwright().start()
            logger.info("Playwright started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Playwright: {str(e)}")
            return False
    
    async def launch_browser(self, headless: Optional[bool] = None) -> bool:
        """
        Launch browser with specified configuration.
        
        Args:
            headless (Optional[bool]): Override headless mode from config
            
        Returns:
            bool: True if browser launched successfully, False otherwise
        """
        if not self.playwright:
            logger.error("Playwright not started. Call start_playwright() first.")
            return False
            
        try:
            # Use provided headless value or default from config
            is_headless = headless if headless is not None else self.config.get("default_headless", True)
            browser_type = self.config.get("browser", "chromium")
            
            # Browser launch options
            launch_options = {
                "headless": is_headless,
                "args": [
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-gpu",
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor"
                ]
            }
            
            # Launch browser based on type
            if browser_type == "chromium":
                self.browser = await self.playwright.chromium.launch(**launch_options)
            elif browser_type == "firefox":
                self.browser = await self.playwright.firefox.launch(**launch_options)
            elif browser_type == "webkit":
                self.browser = await self.playwright.webkit.launch(**launch_options)
            else:
                logger.error(f"Unsupported browser type: {browser_type}")
                return False
                
            logger.info(f"Browser launched successfully: {browser_type} (headless: {is_headless})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to launch browser: {str(e)}")
            return False
    
    async def create_context(self) -> bool:
        """
        Create a new browser context with viewport settings.
        
        Returns:
            bool: True if context created successfully, False otherwise
        """
        if not self.browser:
            logger.error("Browser not launched. Call launch_browser() first.")
            return False
            
        try:
            viewport = self.config.get("viewport", {"width": 1920, "height": 1080})
            
            self.context = await self.browser.new_context(
                viewport=viewport,
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )
            
            logger.info(f"Browser context created with viewport: {viewport}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create browser context: {str(e)}")
            return False
    
    async def create_page(self) -> bool:
        """
        Create a new page in the current context.
        
        Returns:
            bool: True if page created successfully, False otherwise
        """
        if not self.context:
            logger.error("Browser context not created. Call create_context() first.")
            return False
            
        try:
            self.page = await self.context.new_page()
            
            # Set default timeout
            timeout = self.config.get("timeout", 30000)
            self.page.set_default_timeout(timeout)
            
            logger.info(f"Page created successfully with timeout: {timeout}ms")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create page: {str(e)}")
            return False
    
    async def navigate_to_url(self, url: str) -> bool:
        """
        Navigate to the specified URL.
        
        Args:
            url (str): URL to navigate to
            
        Returns:
            bool: True if navigation successful, False otherwise
        """
        if not self.page:
            logger.error("Page not created. Call create_page() first.")
            return False
            
        try:
            await self.page.goto(url, wait_until="networkidle")
            logger.info(f"Successfully navigated to: {url}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to navigate to {url}: {str(e)}")
            return False
    
    async def initialize_full_session(self, headless: Optional[bool] = None) -> bool:
        """
        Initialize a complete Playwright session (start -> launch -> context -> page).
        
        Args:
            headless (Optional[bool]): Override headless mode from config
            
        Returns:
            bool: True if all initialization steps successful, False otherwise
        """
        steps = [
            ("Starting Playwright", self.start_playwright()),
            ("Launching browser", self.launch_browser(headless)),
            ("Creating context", self.create_context()),
            ("Creating page", self.create_page())
        ]
        
        for step_name, step_coro in steps:
            logger.info(f"Executing: {step_name}")
            if not await step_coro:
                logger.error(f"Failed at step: {step_name}")
                await self.cleanup()
                return False
                
        logger.info("Playwright session initialized successfully")
        return True
    
    async def cleanup(self) -> None:
        """Clean up all Playwright resources."""
        try:
            if self.page:
                await self.page.close()
                self.page = None
                logger.info("Page closed")
                
            if self.context:
                await self.context.close()
                self.context = None
                logger.info("Context closed")
                
            if self.browser:
                await self.browser.close()
                self.browser = None
                logger.info("Browser closed")
                
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
                logger.info("Playwright stopped")
                
        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")
    
    def get_page(self) -> Optional[Page]:
        """
        Get the current page instance.
        
        Returns:
            Optional[Page]: Current page or None if not initialized
        """
        return self.page
    
    def is_ready(self) -> bool:
        """
        Check if the Playwright session is ready for operations.
        
        Returns:
            bool: True if session is ready, False otherwise
        """
        return all([
            self.playwright is not None,
            self.browser is not None,
            self.context is not None,
            self.page is not None
        ])
