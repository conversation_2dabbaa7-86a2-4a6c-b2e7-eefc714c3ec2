"""
Playwright Operations Module

This module contains all the specific automation operations for the BeiGene
access management system. Each operation is implemented as a separate function
following functional programming principles.

Author: BeiGene IT Help Desk Service
Date: 2025-08-28
"""

from typing import Optional, Dict, Any, List
from playwright.async_api import Page
from loguru import logger

from .validators import (
    validate_page_loaded,
    validate_element_exists,
    validate_element_clickable,
    validate_input_field,
    validate_modal_dialog,
    validate_switch_state
)
from .auth_handler import auth_handler


# DOM Selectors Configuration
DOM_SELECTORS = {
    "new_button": [
        "button:has-text('New')",
        "button.ant-btn:has-text('New')",
        "button[aria-label*='New']",
        "button.ant-btn.ant-btn-primary:has-text('New')",
        "button.ant-btn.css-iyixax.ant-btn-primary.ant-btn-color-primary.ant-btn-variant-solid"
    ],
    "username_input": [
        "input#userName",
        "input[name='userName']",
        "input[placeholder*='username']",
        "input[placeholder*='用户名']"
    ],
    "realname_input": [
        "input#realName",
        "input[name='realName']",
        "input[placeholder*='real name']",
        "input[placeholder*='真实姓名']"
    ],
    "email_input": [
        "input#email",
        "input[name='email']",
        "input[type='email']",
        "input[placeholder*='email']",
        "input[placeholder*='邮箱']"
    ],
    "aad_switch": [
        "button#isAad",
        "button[role='switch']",
        ".ant-switch",
        "input[type='checkbox'][name*='aad']"
    ],
    "ok_button": [
        "button:has-text('OK')",
        "button:has-text('确定')",
        "button.ant-btn:has-text('OK')",
        "button[type='submit']",
        "button.ant-btn.ant-btn-primary:has-text('OK')"
    ],
    "modal_dialog": [
        ".ant-modal",
        ".modal",
        "[role='dialog']",
        ".dialog"
    ],
    "form_container": [
        ".ant-form",
        "form",
        ".form-container"
    ]
}


async def navigate_to_admin_portal(page: Page, url: str) -> bool:
    """
    Navigate to the BeiGene admin portal URL with authentication handling.

    This function performs the first step of Operation Group I:
    - Navigate to the specified admin portal URL
    - Handle authentication if required
    - Wait for page to load completely
    - Validate that the correct page is loaded

    Args:
        page (Page): Playwright page object
        url (str): Admin portal URL to navigate to

    Returns:
        bool: True if navigation successful, False otherwise
    """
    logger.info(f"Step 1: Navigating to admin portal: {url}")

    try:
        # Navigate to the URL
        await page.goto(url, wait_until="networkidle", timeout=30000)

        # Check if authentication is required
        if await auth_handler.is_authentication_required(page):
            logger.info("Authentication required - please complete login in the browser")

            # Handle authentication flow
            if not await auth_handler.handle_authentication_flow(page, interactive=True):
                logger.error("Authentication failed or timed out")
                return False

        # Verify we have access to the admin portal
        if not await auth_handler.verify_admin_portal_access(page):
            logger.error("Failed to access admin portal after authentication")
            return False

        logger.info("Successfully navigated to admin portal")
        return True

    except Exception as e:
        logger.error(f"Navigation failed: {str(e)}")
        return False


async def click_new_button(page: Page) -> bool:
    """
    Click the "New" button to open the user creation form.

    This function performs the second step of Operation Group I:
    - Locate the "New" button using multiple selector options
    - Validate that the button is clickable
    - Click the button to open the modal dialog
    - Wait for the modal to appear

    Args:
        page (Page): Playwright page object

    Returns:
        bool: True if button clicked successfully and modal opened, False otherwise
    """
    logger.info("Step 2: Clicking New button")

    try:
        new_button_selectors = DOM_SELECTORS["new_button"]

        # Validate button exists and is clickable
        is_clickable, working_selector = await validate_element_clickable(page, new_button_selectors)
        if not is_clickable:
            logger.error("New button is not clickable with any selector")
            return False

        # Click the New button
        await page.click(working_selector)
        logger.info(f"New button clicked using selector: {working_selector}")

        # Wait for modal dialog to appear
        modal_selectors = DOM_SELECTORS["modal_dialog"]
        await page.wait_for_timeout(1000)  # Give modal time to appear

        # Check if modal appeared using any of the selectors
        modal_found = False
        for modal_selector in modal_selectors:
            try:
                if await page.locator(modal_selector).count() > 0:
                    modal_found = True
                    logger.info(f"Modal dialog found with selector: {modal_selector}")
                    break
            except:
                continue

        if not modal_found:
            logger.error("Modal dialog did not appear after clicking New button")
            return False

        logger.info("Modal dialog opened successfully")
        return True

    except Exception as e:
        logger.error(f"Failed to click New button: {str(e)}")
        return False


async def fill_user_form(page: Page, email_address: str) -> bool:
    """
    Fill the user creation form with the provided email address.
    
    This function performs the third step of Operation Group I:
    - Fill username field with email address
    - Fill real name field with email address  
    - Toggle the AAD switch to enabled state
    - Fill email field with email address
    - Click OK button to submit the form
    
    Args:
        page (Page): Playwright page object
        email_address (str): Email address to use for user creation
        
    Returns:
        bool: True if form filled and submitted successfully, False otherwise
    """
    logger.info(f"Step 3: Filling user form with email: {email_address}")
    
    try:
        # Fill username field
        if not await fill_username_field(page, email_address):
            return False
            
        # Fill real name field
        if not await fill_realname_field(page, email_address):
            return False
            
        # Toggle AAD switch
        if not await toggle_aad_switch(page):
            return False
            
        # Fill email field
        if not await fill_email_field(page, email_address):
            return False
            
        # Click OK button
        if not await click_ok_button(page):
            return False
            
        logger.info("User form filled and submitted successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to fill user form: {str(e)}")
        return False


async def fill_username_field(page: Page, email_address: str) -> bool:
    """
    Fill the username input field with the provided email address.
    
    Args:
        page (Page): Playwright page object
        email_address (str): Email address to enter
        
    Returns:
        bool: True if field filled successfully, False otherwise
    """
    logger.info("Filling username field")
    
    try:
        username_selector = DOM_SELECTORS["username_input"]
        
        # Validate input field is ready
        if not await validate_input_field(page, username_selector):
            logger.error("Username field is not ready for input")
            return False
            
        # Clear and fill the field
        await page.fill(username_selector, email_address)
        
        # Verify the value was set correctly
        entered_value = await page.input_value(username_selector)
        if entered_value != email_address:
            logger.error(f"Username field value mismatch. Expected: {email_address}, Got: {entered_value}")
            return False
            
        logger.info(f"Username field filled with: {email_address}")
        return True

    except Exception as e:
        logger.error(f"Failed to fill username field: {str(e)}")
        return False


async def fill_realname_field(page: Page, email_address: str) -> bool:
    """
    Fill the real name input field with the provided email address.

    Args:
        page (Page): Playwright page object
        email_address (str): Email address to enter

    Returns:
        bool: True if field filled successfully, False otherwise
    """
    logger.info("Filling real name field")

    try:
        realname_selector = DOM_SELECTORS["realname_input"]

        # Validate input field is ready
        if not await validate_input_field(page, realname_selector):
            logger.error("Real name field is not ready for input")
            return False

        # Clear and fill the field
        await page.fill(realname_selector, email_address)

        # Verify the value was set correctly
        entered_value = await page.input_value(realname_selector)
        if entered_value != email_address:
            logger.error(f"Real name field value mismatch. Expected: {email_address}, Got: {entered_value}")
            return False

        logger.info(f"Real name field filled with: {email_address}")
        return True

    except Exception as e:
        logger.error(f"Failed to fill real name field: {str(e)}")
        return False


async def toggle_aad_switch(page: Page) -> bool:
    """
    Toggle the AAD switch to enabled state.

    Args:
        page (Page): Playwright page object

    Returns:
        bool: True if switch toggled successfully, False otherwise
    """
    logger.info("Toggling AAD switch")

    try:
        aad_switch_selector = DOM_SELECTORS["aad_switch"]

        # Validate switch exists and is clickable
        if not await validate_element_clickable(page, aad_switch_selector):
            logger.error("AAD switch is not clickable")
            return False

        # Check current state of the switch
        current_state = await page.get_attribute(aad_switch_selector, "aria-checked")
        is_currently_enabled = current_state == "true"

        # If switch is already enabled, no need to click
        if is_currently_enabled:
            logger.info("AAD switch is already enabled")
            return True

        # Click to enable the switch
        await page.click(aad_switch_selector)

        # Wait a moment for the state to update
        await page.wait_for_timeout(500)

        # Validate the switch is now enabled
        if not await validate_switch_state(page, aad_switch_selector, True):
            logger.error("Failed to enable AAD switch")
            return False

        logger.info("AAD switch enabled successfully")
        return True

    except Exception as e:
        logger.error(f"Failed to toggle AAD switch: {str(e)}")
        return False


async def fill_email_field(page: Page, email_address: str) -> bool:
    """
    Fill the email input field with the provided email address.

    Args:
        page (Page): Playwright page object
        email_address (str): Email address to enter

    Returns:
        bool: True if field filled successfully, False otherwise
    """
    logger.info("Filling email field")

    try:
        email_selector = DOM_SELECTORS["email_input"]

        # Validate input field is ready
        if not await validate_input_field(page, email_selector):
            logger.error("Email field is not ready for input")
            return False

        # Clear and fill the field
        await page.fill(email_selector, email_address)

        # Verify the value was set correctly
        entered_value = await page.input_value(email_selector)
        if entered_value != email_address:
            logger.error(f"Email field value mismatch. Expected: {email_address}, Got: {entered_value}")
            return False

        logger.info(f"Email field filled with: {email_address}")
        return True

    except Exception as e:
        logger.error(f"Failed to fill email field: {str(e)}")
        return False


async def click_ok_button(page: Page) -> bool:
    """
    Click the OK button to submit the user creation form.

    Args:
        page (Page): Playwright page object

    Returns:
        bool: True if button clicked successfully, False otherwise
    """
    logger.info("Clicking OK button")

    try:
        ok_button_selector = DOM_SELECTORS["ok_button"]

        # Validate button exists and is clickable
        if not await validate_element_clickable(page, ok_button_selector):
            logger.error("OK button is not clickable")
            return False

        # Click the OK button
        await page.click(ok_button_selector)
        logger.info("OK button clicked")

        # Wait for form submission to complete
        await page.wait_for_timeout(2000)

        logger.info("Form submitted successfully")
        return True

    except Exception as e:
        logger.error(f"Failed to click OK button: {str(e)}")
        return False


# High-level Operation Group Functions

async def execute_operation_group_i(page: Page, beigene_email: str) -> bool:
    """
    Execute Operation Group I for @beigene.com email address.

    This function orchestrates all steps for creating a user with @beigene.com domain:
    1. Navigate to admin portal
    2. Click New button
    3. Fill and submit user form

    Args:
        page (Page): Playwright page object
        beigene_email (str): Email address with @beigene.com domain

    Returns:
        bool: True if all operations completed successfully, False otherwise
    """
    logger.info(f"Starting Operation Group I for: {beigene_email}")

    # Define the admin portal URL
    admin_url = "https://rdai.beigenecorp.net/admin/#/admin/organization/user"

    try:
        # Step 1: Navigate to admin portal
        if not await navigate_to_admin_portal(page, admin_url):
            logger.error("Operation Group I failed at navigation step")
            return False

        # Step 2: Click New button
        if not await click_new_button(page):
            logger.error("Operation Group I failed at New button step")
            return False

        # Step 3: Fill user form
        if not await fill_user_form(page, beigene_email):
            logger.error("Operation Group I failed at form filling step")
            return False

        logger.info(f"Operation Group I completed successfully for: {beigene_email}")
        return True

    except Exception as e:
        logger.error(f"Operation Group I failed with exception: {str(e)}")
        return False


async def execute_operation_group_ii(page: Page, beonemed_email: str) -> bool:
    """
    Execute Operation Group II for @beonemed.com email address.

    This function orchestrates all steps for creating a user with @beonemed.com domain.
    The operations are identical to Group I but with different email domain.

    Args:
        page (Page): Playwright page object
        beonemed_email (str): Email address with @beonemed.com domain

    Returns:
        bool: True if all operations completed successfully, False otherwise
    """
    logger.info(f"Starting Operation Group II for: {beonemed_email}")

    # Define the admin portal URL (same as Group I)
    admin_url = "https://rdai.beigenecorp.net/admin/#/admin/organization/user"

    try:
        # Step 1: Navigate to admin portal (if not already there)
        current_url = page.url
        if "admin/organization/user" not in current_url:
            if not await navigate_to_admin_portal(page, admin_url):
                logger.error("Operation Group II failed at navigation step")
                return False
        else:
            logger.info("Already on admin portal page, skipping navigation")

        # Step 2: Click New button
        if not await click_new_button(page):
            logger.error("Operation Group II failed at New button step")
            return False

        # Step 3: Fill user form
        if not await fill_user_form(page, beonemed_email):
            logger.error("Operation Group II failed at form filling step")
            return False

        logger.info(f"Operation Group II completed successfully for: {beonemed_email}")
        return True

    except Exception as e:
        logger.error(f"Operation Group II failed with exception: {str(e)}")
        return False


async def execute_complete_user_creation_workflow(page: Page, email_list: List[str]) -> Dict[str, bool]:
    """
    Execute the complete user creation workflow for a list of email addresses.

    This function processes a list of email addresses and creates users for both
    @beigene.com and @beonemed.com domains by executing Operation Groups I and II.

    Args:
        page (Page): Playwright page object
        email_list (List[str]): List of email addresses to process

    Returns:
        Dict[str, bool]: Dictionary mapping email addresses to success status
    """
    logger.info(f"Starting complete workflow for {len(email_list)} email addresses")

    results = {}

    for email in email_list:
        logger.info(f"Processing email: {email}")

        try:
            if "@beigene.com" in email.lower():
                # Execute Operation Group I for beigene.com
                success = await execute_operation_group_i(page, email)
                results[email] = success

            elif "@beonemed.com" in email.lower():
                # Execute Operation Group II for beonemed.com
                success = await execute_operation_group_ii(page, email)
                results[email] = success

            else:
                logger.error(f"Unsupported email domain: {email}")
                results[email] = False

            # Add delay between operations to avoid overwhelming the server
            if len(email_list) > 1:
                await page.wait_for_timeout(1000)

        except Exception as e:
            logger.error(f"Failed to process email {email}: {str(e)}")
            results[email] = False

    # Log summary
    successful_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    logger.info(f"Workflow completed: {successful_count}/{total_count} emails processed successfully")

    return results
