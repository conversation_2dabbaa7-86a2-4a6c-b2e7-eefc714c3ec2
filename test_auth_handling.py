"""
Authentication Handling Test Script

This script tests the authentication handling functionality
without running the full automation workflow.

Author: BeiGene IT Help Desk Service
Date: 2025-08-28
"""

import sys
import asyncio
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_auth_detection():
    """Test authentication detection functionality."""
    print("Testing authentication detection...")
    
    try:
        from src.automation.playwright_manager import PlaywrightManager
        from src.automation.auth_handler import auth_handler
        
        # Initialize Playwright
        manager = PlaywrightManager()
        
        print("Initializing Playwright session...")
        if not await manager.initialize_full_session(headless=False):  # Use headed mode for testing
            print("✗ Failed to initialize Playwright session")
            return False
        
        page = manager.get_page()
        if not page:
            print("✗ Failed to get page instance")
            return False
        
        # Navigate to admin portal
        admin_url = "https://rdai.beigenecorp.net/admin/#/admin/organization/user"
        print(f"Navigating to: {admin_url}")
        
        await page.goto(admin_url, wait_until="networkidle", timeout=30000)
        
        # Check authentication status
        auth_required = await auth_handler.is_authentication_required(page)
        print(f"Authentication required: {auth_required}")
        
        current_url = page.url
        print(f"Current URL: {current_url}")
        
        if auth_required:
            print("✓ Authentication detection working - login required")
            print("Note: This is expected for the BeiGene portal")
        else:
            print("✓ Authentication detection working - already authenticated")
        
        # Clean up
        await manager.cleanup()
        return True
        
    except Exception as e:
        print(f"✗ Authentication test failed: {str(e)}")
        return False


async def test_selector_improvements():
    """Test the improved DOM selectors."""
    print("\nTesting improved DOM selectors...")
    
    try:
        from src.automation.operations import DOM_SELECTORS
        
        print("Checking selector configuration:")
        for element_name, selectors in DOM_SELECTORS.items():
            if isinstance(selectors, list):
                print(f"✓ {element_name}: {len(selectors)} fallback selectors")
            else:
                print(f"! {element_name}: Single selector (should be list)")
        
        return True
        
    except Exception as e:
        print(f"✗ Selector test failed: {str(e)}")
        return False


def test_auth_handler_import():
    """Test that auth handler can be imported."""
    print("\nTesting auth handler import...")
    
    try:
        from src.automation.auth_handler import auth_handler, AuthenticationHandler
        
        print("✓ AuthenticationHandler class imported")
        print("✓ auth_handler instance imported")
        
        # Test basic properties
        handler = AuthenticationHandler()
        print(f"✓ Microsoft login patterns: {len(handler.microsoft_login_patterns)}")
        print(f"✓ Admin portal patterns: {len(handler.admin_portal_patterns)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Auth handler import failed: {str(e)}")
        return False


async def main():
    """Run all authentication-related tests."""
    print("=" * 60)
    print("FastMode Access Manager - Authentication Handling Tests")
    print("=" * 60)
    
    tests = [
        ("Auth Handler Import", test_auth_handler_import),
        ("Selector Improvements", test_selector_improvements),
        ("Auth Detection (Live)", test_auth_detection)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All authentication tests passed!")
        print("\n📋 Next Steps:")
        print("1. Run the main application: python main.py")
        print("2. Uncheck 'Headless Mode' for first-time use")
        print("3. Enter a test email and click START")
        print("4. Complete the login process when prompted")
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        sys.exit(0)
